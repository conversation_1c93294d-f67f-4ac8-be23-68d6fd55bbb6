import classnames from 'classnames';
import React, { useCallback, useMemo, FC } from 'react';

import Scroll from '../../../components/other/Scroll';
import Empty from '../../../components/utils/Empty';
import Spinner from '../../../components/utils/Spinner';
import styles from '../infinityScroll.scss';
import _FetchMoreButton from './FetchMoreButton';

import TFetchMode from './abstract/TFetchMode';
import IFetchMoreButton from './abstract/IFetchMoreButton';
import { DefaultPageSize } from '../../../components/other/PageSizeSelect';

interface IFetchMoreScrollContent<T> {
  renderItems: Function;
  height?: number;
  className?: string;
  mode: TFetchMode;
  itemsPerFetch?: number;
  isSkip?: boolean;
  initialItems?: T[];
  items?: T[];
  totalCount: number;
  onNewDataLoaded?: (items: T[]) => void;
  FetchMoreButton?: FC<IFetchMoreButton>;
  hasEmptyPlaceholder?: boolean;
  hasMore: boolean;
  errorMessage?: string;
  isLoading?: boolean;
  isLoadMoreLoading: boolean;
  onLoadMore: () => void;
}

function FetchMoreScrollContent<T>({
  className,
  height,
  renderItems,
  mode,
  itemsPerFetch = DefaultPageSize,
  FetchMoreButton = _FetchMoreButton,
  initialItems = [],
  items: _items = [],
  hasEmptyPlaceholder = true,
  hasMore,
  errorMessage,
  isLoading,
  isLoadMoreLoading,
  onLoadMore,
  totalCount,
}: IFetchMoreScrollContent<T>) {
  const items = _items || initialItems;

  const renderSpinner = useCallback(
    () => (
      <div className={styles.loading}>
        <Spinner />
      </div>
    ),
    [],
  );

  const renderEmpty = useCallback(
    () => (
      <div className={styles.empty}>
        <Empty />
      </div>
    ),
    [],
  );

  const isEmpty = useMemo(
    () => !isLoading && !items.length && hasEmptyPlaceholder,
    [items.length, isLoading, hasEmptyPlaceholder],
  );

  const restCount = totalCount - initialItems.length;

  return (
    <Scroll
      autoHeightMax={height}
      className={classnames(styles.scroll, className)}
    >
      {mode === 'DESC' && (
        <FetchMoreButton
          hasMore={hasMore}
          isLoadMoreLoading={isLoadMoreLoading}
          itemsPerFetch={Math.min(itemsPerFetch, restCount)}
          mode={mode}
          restCount={restCount}
          onLoadMore={onLoadMore}
        />
      )}
      {mode === 'DESC' && isLoadMoreLoading ? renderSpinner() : null}
      {errorMessage}
      {isEmpty && !isLoadMoreLoading ? renderEmpty() : null}
      {isLoading ? renderSpinner() : null}
      {!isEmpty ? renderItems(items) : null}
      {mode === 'ASC' && isLoadMoreLoading ? renderSpinner() : null}
      {mode === 'ASC' && (
        <FetchMoreButton
          hasMore={hasMore}
          isLoadMoreLoading={isLoadMoreLoading}
          itemsPerFetch={itemsPerFetch}
          mode={mode}
          restCount={Math.min(itemsPerFetch, restCount)}
          onLoadMore={onLoadMore}
        />
      )}
    </Scroll>
  );
}

export default FetchMoreScrollContent;
