/* global process */
import classnames from 'classnames';
import React from 'react';
import uuid from 'uuid';
import { TIcon, TTooltipPosition } from '../../propTypes';
import styles from './BadgeTooltip.scss';
import Icon, { TIconType } from './Icon';

import Popover from './Popover';
import Spinner from './Spinner';

export interface IBadgeTooltip {
  title?: string | number | JSX.Element;
  position?: TTooltipPosition;
  children: React.ReactNode;
  trigger?: 'hover' | 'click';
  icon?: TIcon;
  hasNoLabel?: boolean;
  isAuditHistory?: boolean;
  isCloseButton?: boolean;
  isDisableClick?: boolean;
  modelId?: number | string;
  tooltipClassName?: string;
  iconType?: TIconType;
  iconClassName?: string;
  offsetTop?: number;
  isTransparent?: boolean;
  contentWrapperClassName?: string;
  onOpen?: () => void;
  onClose?: () => void;
}

type TState = {
  popoverOpen: boolean;
  elId?: string;
};
export default class BadgeTooltip extends React.Component<
  IBadgeTooltip,
  TState
> {
  static defaultProps = {
    position: 'left',
    trigger: 'hover',
    icon: null,
    hasNoLabel: false,
    isAuditHistory: false,
    isDisableClick: false,
    modelId: null,
    title: '',
    isCloseButton: false,
    tooltipClassName: null,
    iconType: '',
    iconClassName: '',
    offsetTop: 0,
    isTransparent: false,
    onOpen: null,
    onClose: null,
  };

  state = {
    popoverOpen: false,
    elId: '',
  };

  componentDidMount() {
    if (process.env.BROWSER) {
      const { modelId } = this.props;
      const elId = modelId ? `tooltip-${modelId}` : `tooltip-${uuid.v4()}`;
      /* eslint-disable react/no-did-mount-set-state */
      this.setState({ elId });
    }
  }

  handleOpenPopover = e => {
    e.preventDefault();
    e.stopPropagation();
    this.setState({ popoverOpen: true });
    const { onOpen } = this.props;
    if (onOpen) {
      onOpen();
    }
  };

  handleClosePopover = e => {
    e.preventDefault();
    this.setState({ popoverOpen: false });
    const { onClose } = this.props;
    if (onClose) {
      onClose();
    }
  };

  render() {
    const {
      title,
      position,
      children,
      trigger,
      icon,
      hasNoLabel,
      isAuditHistory,
      isCloseButton,
      tooltipClassName,
      iconType,
      iconClassName,
      offsetTop,
      isTransparent,
      contentWrapperClassName,
      isDisableClick,
    } = this.props;
    const { popoverOpen, elId } = this.state;
    if (!elId) {
      return <Spinner />;
    }

    const wrapperProps =
      trigger === 'hover'
        ? {
            onMouseMove: this.handleOpenPopover,
            onMouseLeave: this.handleClosePopover,
          }
        : {};
    const tooltipProps =
      trigger === 'hover'
        ? { key: `Hovertooltip_${elId}` }
        : { key: `Clicktooltip_${elId}` };

    return (
      <div
        className={styles.tooltipWrapper}
        onClick={isDisableClick ? undefined : this.handleOpenPopover}
        {...wrapperProps}
      >
        <span
          className={classnames('valign-top', styles.label, tooltipClassName, {
            'ml-5': !isTransparent,
            'label label-success ': !hasNoLabel,
            [styles.labelText]: !icon && !isTransparent,
            [styles.auditHistoryButton]: isAuditHistory,
          })}
          data-toggle="popover"
          id={elId}
          {...tooltipProps}
        >
          {icon ? (
            <Icon className={iconClassName} name={icon} type={iconType} />
          ) : (
            title
          )}
        </span>
        <Popover
          key={`popover_${elId}`}
          anchorId={elId}
          contentWrapperClassName={contentWrapperClassName}
          isCloseButton={isCloseButton}
          offsetTop={offsetTop}
          position={position}
          visible={popoverOpen}
          onClose={this.handleClosePopover}
        >
          {children}
        </Popover>
      </div>
    );
  }
}
