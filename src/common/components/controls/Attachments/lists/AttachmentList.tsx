import React, { FC, useMemo } from 'react';
import classnames from 'classnames';

import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';

import { TDropdownActions } from '../../Dropdown';
import IAttachmentItemComponentProps from '../abstract/IAttachmentItemComponentProps';
import AttachmentItem from '../listItems/AttachmentItem';
import styles from './AttachmentList.scss';

const AttachmentList: FC<IAttachmentListProps> = ({
  isDownloadable = false,
  onRemove,
  children,
  attachmentPreviewComponent,
  hasLargeIcons,
  editable,
  attachments,
  attachmentItemComponent,
  onChange,
  dropdownOptions,
  maxDescriptionLength,
  hideIcons,
  hasLowStrike,
  listClassName,
  onSplitAudio,
  onImageAiImprovement,
  allowedNumberOfFiles,
}) => {
  const AttachmentItemComponent = attachmentItemComponent || AttachmentItem;

  const hasSplitAudio = useMemo(
    () => allowedNumberOfFiles && allowedNumberOfFiles > attachments.length,
    [allowedNumberOfFiles, attachments],
  );

  return (
    <div className="component-attachment-files">
      <div className="attachments-container">
        <ul
          className={classnames(
            'attached-area',
            'attached-area-overide',
            styles.attachmentContainer,
            listClassName,
          )}
        >
          {attachments.map((attachment, index) => (
            <AttachmentItemComponent
              key={attachment.fileId || attachment.uploadToken}
              attachment={attachment}
              attachmentPreviewComponent={attachmentPreviewComponent}
              dropdownOptions={
                dropdownOptions ? dropdownOptions(attachment) : []
              }
              editable={editable}
              hasLargeIcons={hasLargeIcons}
              hasLowStrike={hasLowStrike}
              hideIcons={hideIcons}
              index={index}
              isDownloadable={isDownloadable}
              maxDescriptionLength={maxDescriptionLength}
              onChange={onChange}
              onImageAiImprovement={onImageAiImprovement}
              onRemove={onRemove}
              onSplitAudio={hasSplitAudio ? onSplitAudio : undefined}
            />
          ))}
          {children}
        </ul>
      </div>
    </div>
  );
};

export default AttachmentList;

export interface IAttachmentListProps {
  editable: boolean;
  onRemove?: Function;
  attachments: IFileAttachmentTemp[];
  isDownloadable?: boolean;
  attachmentPreviewComponent?: React.ComponentType;
  hasLargeIcons?: boolean;
  attachmentItemComponent?: React.ComponentType<IAttachmentItemComponentProps>;
  dropdownOptions?: (data: IFileAttachmentTemp) => TDropdownActions[];
  onChange?: (attachments: IFileAttachmentTemp) => void;
  maxDescriptionLength?: number;
  hideIcons?: boolean;
  hasLowStrike?: boolean;
  listClassName?: string;
  onSplitAudio?: (
    attachment: IFileAttachmentTemp,
    splittedFiles: string[],
  ) => Promise<void>;
  onImageAiImprovement?: (
    attachment: IFileAttachmentTemp,
    image: string,
  ) => Promise<void>;
  allowedNumberOfFiles?: number;
}
