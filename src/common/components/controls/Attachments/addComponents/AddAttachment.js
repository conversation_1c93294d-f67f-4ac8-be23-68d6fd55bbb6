import classnames from 'classnames';
import PropTypes from 'prop-types';
import React from 'react';
import Icon from '../../../utils/Icon';
import Tooltip from '../../../utils/Tooltip';

import styles from '../Attachments.scss';

export default class AddAttachment extends React.PureComponent {
  static propTypes = {
    editable: PropTypes.bool.isRequired,
    onModalOpen: PropTypes.func.isRequired,
    isInPage: PropTypes.bool,
    isVisible: PropTypes.bool,
    name: PropTypes.string,
    isCentered: PropTypes.bool,
    isDisabled: PropTypes.bool,
    addButtonTitle: PropTypes.string,
  };

  static defaultProps = {
    isInPage: false,
    isVisible: false,
    name: 'file-plus',
    isCentered: false,
    isDisabled: false,
    addButtonTitle: '',
  };

  render() {
    const {
      onModalOpen,
      editable,
      isInPage,
      isVisible,
      name,
      isCentered,
      isDisabled,
      addButtonTitle,
    } = this.props;

    if (isVisible && isInPage) {
      return null;
    }

    const tooltipContent = function () {
      return <span>Attach File</span>;
    };

    return (
      <div
        className={classnames(
          {
            'component-attachment-button-w-eddrive mt-3': !isCentered,
            [styles.centerWrapper]: isCentered,
            [styles.disabled]: isDisabled,
          },
          styles.attachButton,
        )}
        hidden={!editable}
      >
        <ul
          className={classnames('attachment-button icons-list mr-10', {
            [styles.centerButton]: isCentered,
          })}
        >
          <li className="attach">
            <Tooltip render={tooltipContent}>
              <a
                className={classnames(styles.iconPosition, {
                  [styles.transparentTheme]: name === 'plus3',
                })}
                title={addButtonTitle}
                onClick={isDisabled ? undefined : onModalOpen}
              >
                <Icon
                  className={classnames(styles.iconColor, {
                    [styles.f_20]: name === 'plus3',
                  })}
                  name={name}
                />
              </a>
            </Tooltip>
          </li>
        </ul>
      </div>
    );
  }
}
