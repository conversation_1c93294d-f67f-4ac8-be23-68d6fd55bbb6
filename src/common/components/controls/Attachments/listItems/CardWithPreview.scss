.main {
  border: 1px solid #D7D7D7;
  width: 150px;
  height: 170px;
  margin: 0 10px 10px 0;
  float: left;
}
.cardHeader {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.cardName {
  font-size: 12px;
}

.icon {
  width: 100%;
  font-size: 40px;
  padding: 25px 10px;
  cursor: pointer;
}

.previewIcon {
  font-size: 80px;
}

.previewBody {
  display: flex;
}

.link {
  color: black !important;
  text-decoration: none;
  display: block;
  width: 100%;

  &:visited,:link,:hover,:active {
    color: black;
    text-decoration: none;
  }
}

.itemWrapper {
  >a {
    cursor: auto !important;
  }
}
