import { MutationHookOptions } from '@apollo/react-hooks/lib/types';
import { DocumentNode } from 'graphql';
import React, { FC, useCallback, useMemo, useState } from 'react';
import { useMutation } from 'react-apollo';
import classNames from 'classnames';

import { IFileAttachmentTemp } from '../../../../abstract/IFileAttachments';
import { TPerson } from '../../../utils/PersonAvatar';
import IAttachmentItemComponentProps from '../abstract/IAttachmentItemComponentProps';
import { dummyMutation } from '../../../../data/dummyMutation';
import useT from '../../../utils/Translations/useT';
import Dropdown, { IDropdownAction } from '../../Dropdown';
import style from './CardWithPreview.scss';
import { formatValueDueToUnit } from '../../../../../model/sizeConvertation';
import SpinnerError from '../../../utils/SpinnerError';
import Icon from '../../../utils/Icon';
import { TIcon } from '../../../../propTypes';
import Modal from '../../../utils/Modal';
import toEdanaTimestamp4 from '../../../../utils/edanaTimestamp4';

export interface IFileAttachmentTempWithPerson extends IFileAttachmentTemp {
  person?: TPerson;
}

interface ICardWithPreview
  extends IAttachmentItemComponentProps<IFileAttachmentTempWithPerson> {
  isDeletable?: boolean;
  onDelete?: (item: IFileAttachmentTempWithPerson) => void;
  cookDeleteVariables?: (
    item: IFileAttachmentTempWithPerson,
  ) => MutationHookOptions;
  deleteGql?: DocumentNode;
  hasPreview?: boolean;
}

const CardWithPreview: FC<ICardWithPreview> = ({
  attachment,
  isDeletable = false,
  onDelete,
  deleteGql,
  cookDeleteVariables,
  hasPreview = true,
}) => {
  const [deleteMutation, { error, loading: isLoading }] = useMutation(
    deleteGql || dummyMutation,
  );

  const [iconName, fileFormat] = useMemo(() => {
    if (attachment.mimeType.includes('pdf')) {
      return ['file-pdf', 'PDF'];
    } else if (attachment.mimeType.includes('ppt')) {
      return ['file-presentation', 'Powerpoint'];
    } else if (attachment.mimeType.includes('zip')) {
      return ['file-zip', 'Zip'];
    } else if (attachment.mimeType.includes('excel')) {
      return ['file-excel', 'Excel'];
    } else if (attachment.mimeType.includes('image')) {
      return ['file-picture', 'Image'];
    } else if (attachment.mimeType.includes('audio')) {
      return ['music3', 'Audio'];
    } else if (attachment.mimeType.includes('video')) {
      return ['file-video', 'Video'];
    } else if (attachment.mimeType.includes('word')) {
      return ['file-word', 'Word'];
    }

    return ['file-empty', 'File'];
  }, [attachment.mimeType]);

  const t = useT();
  const [isPreviewOpen, setPreviewOpen] = useState(false);

  const handleOpenPreview = useCallback(() => {
    if (!hasPreview) {
      return;
    }

    setPreviewOpen(true);
  }, [hasPreview]);

  const handleClosePreview = useCallback(() => {
    setPreviewOpen(false);
  }, []);

  const handleDelete = useCallback(() => {
    deleteGql &&
      deleteMutation(
        cookDeleteVariables ? cookDeleteVariables(attachment) : {},
      );
    onDelete && onDelete(attachment);
  }, [onDelete, attachment, deleteMutation, deleteGql, cookDeleteVariables]);

  const actions = useMemo(() => {
    const actions: IDropdownAction[] = [
      {
        itemWrapperClassName: style.itemWrapper,
        isPreventDefault: false,
        isCLoseOnClick: false,
        item: (
          <a
            download
            className={style.link}
            href={attachment.url}
            rel="noopener noreferrer"
          >
            {t('Download')}
          </a>
        ),
      },
    ];

    if (hasPreview) {
      actions.push({ label: t('Preview'), onClick: handleOpenPreview });
    }

    if (isDeletable) {
      actions.push({
        label: t('Delete'),
        onClick: handleDelete,
      });
    }

    return actions;
  }, [
    attachment.url,
    t,
    handleOpenPreview,
    isDeletable,
    handleDelete,
    hasPreview,
  ]);

  return (
    <div className={classNames(style.main)} onClick={handleOpenPreview}>
      <SpinnerError error={error} inline={false} loading={isLoading}>
        <div className={classNames(style.cardHeader)}>
          <p className={classNames('text-ellipsis', style.cardName)}>
            {attachment.fileName}
          </p>
          <Dropdown noPull actions={actions} />
        </div>
        {hasPreview ? (
          <Icon className={style.icon} name={iconName as TIcon} />
        ) : (
          <a
            download
            className={style.link}
            href={attachment.url}
            rel="noopener noreferrer"
          >
            <Icon className={style.icon} name={iconName as TIcon} />
          </a>
        )}

        <Modal
          isOverlayClickHidesModal
          size="xs"
          title={attachment.fileName}
          visible={isPreviewOpen}
          onClose={handleClosePreview}
        >
          <div className={style.previewBody}>
            <Icon
              className={classNames(style.icon, style.previewIcon)}
              name={iconName as TIcon}
            />
            <div>
              <p>{t('Type: #{fileFormat}', { fileFormat })}</p>
              <p>
                {`${t('Size')}: ${formatValueDueToUnit(
                  t,
                  'auto',
                  attachment.fileSize,
                )}`}
              </p>
              <p>{`${t('Owner')}: ${attachment.person?.fullName}`}</p>
              <p>
                {`${t('Created')}: ${toEdanaTimestamp4(attachment.createdAt)}`}
              </p>
              <p>
                {attachment?.description &&
                  `${t('Description')}: ${attachment.description}`}
              </p>
            </div>
          </div>
        </Modal>
      </SpinnerError>
    </div>
  );
};

export default CardWithPreview;
