import React, { <PERSON> } from 'react';
import { IFileAttachmentTemp } from '../../abstract/IFileAttachments';
import { TIcon } from '../../propTypes';
import AttachFile from './base/AttachFile';

const FormAttachFileField: FC<IFormAttachFileFieldProps> = ({
  onChange,
  value,
  fileCategory,
  isEditable,
  defaultIcon,
}) => (
  <AttachFile
    defaultIcon={defaultIcon}
    fileCategory={fileCategory}
    isEditable={isEditable}
    value={value}
    onChange={onChange}
  />
);

export default FormAttachFileField;

interface IFormAttachFileFieldProps {
  value?: IFileAttachmentTemp;
  onChange?: (value?: IFileAttachmentTemp) => void;
  fileCategory: string;
  isEditable?: boolean;
  defaultIcon?: TIcon;
}
