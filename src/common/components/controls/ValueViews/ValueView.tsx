import classnames from 'classnames';
import { isString } from 'lodash';
import React, { useMemo } from 'react';
import FormControlInfoMessage, {
  TFormControlInfoMessageAlign,
} from '../../utils/FormControlInfoMessage';

import Spinner from '../../utils/Spinner';
import AnimatedTitle from '../base/AnimatedTitle';

import styles from './ValueView.scss';

export interface IValueView {
  isMultiline?: boolean;
  loading?: boolean;
  label?: string;
  noLabel?: boolean;
  value?: React.ReactNode | string;
  emptyValue?: string;
  renderAddon?: () => JSX.Element;
  className?: string;
  infoMessage?: string;
  infoMessageAlign?: TFormControlInfoMessageAlign;
  title?: string;
  addonClassName?: string;
  disableTitleTooltip?: boolean;
}

const ValueView: React.FC<IValueView> = ({
  isMultiline = false,
  loading = false,
  label = '',
  noLabel = false,
  value = null,
  emptyValue = null,
  renderAddon = null,
  addonClassName,
  className = '',
  infoMessage,
  infoMessageAlign = 'left' as TFormControlInfoMessageAlign,
  title = '',
  disableTitleTooltip = false,
}) => {
  const empty = value === null || value === undefined;

  const hasLabel = !noLabel && label;

  const _title = useMemo<string>(() => {
    if (title) {
      return title;
    }

    if (isString(value)) {
      return value as string;
    }

    if (isString(emptyValue)) {
      return emptyValue as string;
    }

    return '';
  }, [emptyValue, title, value]);

  return (
    <div
      className={classnames('form-group-material', className, {
        'form-group': hasLabel,
        [styles.loading]: loading,
        [styles.withAddon]: renderAddon,
      })}
    >
      {hasLabel ? (
        <AnimatedTitle value className={styles.label} placeholder={label} />
      ) : null}
      <div
        className={classnames(styles.value, 'captionLabel', {
          [styles.multiline]: isMultiline,
          'empty-value': empty && !loading,
        })}
        title={disableTitleTooltip ? '' : _title}
      >
        {loading && <Spinner />}
        {!loading && empty && (emptyValue || '')}
        {!loading && !empty && value}
        {renderAddon && (
          <span className={classnames(styles.addon, addonClassName)}>
            {renderAddon()}
          </span>
        )}
        {infoMessage && (
          <FormControlInfoMessage
            additionalClassNames="mt-10"
            message={infoMessage}
            messageAlign={infoMessageAlign}
          />
        )}
      </div>
    </div>
  );
};

export default ValueView;
