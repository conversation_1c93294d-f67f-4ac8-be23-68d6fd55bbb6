import React, {
  ReactChild,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import uuid from 'uuid';
import EntityForm from '../../containers/EntityForm';
import DependsOnField from '../../containers/EntityForm/DependsOnField';
import RadioGroupField from '../../containers/EntityForm/fields/RadioGroupField';
import TextField from '../../containers/EntityForm/fields/TextField';

import Popover from '../../utils/Popover';

import useT from '../../utils/Translations/useT';
import style from './MessageReport.scss';
import ReportOptions, { OTHER, SPAM, TReportOptions } from './ReportOptions';

interface IMessageReport {
  children: ReactChild;
  isOpen?: boolean;
  onClose?: Function;
  onOpen?: Function;
  onSubmit: (entity: IMessageReportEntity) => void;
}

interface IMessageReportEntity {
  reason: TReportOptions;
  description?: string;
  id: null | number;
}

export default function MessageReport({
  children,
  isOpen,
  onClose,
  onOpen,
  onSubmit,
}: IMessageReport) {
  const t = useT();
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);

  const handleClosePopover = useCallback(() => {
    setPopoverOpen(false);
    onClose && onClose();
  }, [setPopoverOpen, onClose]);

  const handleOpenPopover = useCallback(() => {
    setPopoverOpen(true);
    onOpen && onOpen();
  }, [setPopoverOpen, onOpen]);

  const idRef = useRef(`message-report-${uuid.v4()}`);

  const visible = isOpen === undefined ? popoverOpen : isOpen;
  const entity = useMemo<IMessageReportEntity>(
    () => ({
      reason: SPAM.value,
      description: '',
      id: null,
    }),
    [],
  );

  return (
    <div data-toggle="popover" id={idRef.current} onClick={handleOpenPopover}>
      {children}
      <Popover
        key={`popover_${idRef.current}`}
        isCloseButton
        anchorId={idRef.current}
        contentWrapperClassName={style.form}
        position="left"
        visible={visible}
        onClose={handleClosePopover}
      >
        <h6>{t('Report Message')}</h6>
        <EntityForm<IMessageReportEntity>
          isStoppedEventOutsideForm
          createLabel={t('Report')}
          entity={entity}
          successMessageText={t('Reported')}
          onCancel={handleClosePopover}
          onSubmit={onSubmit}
        >
          <RadioGroupField
            required
            columns={1}
            name="reason"
            options={ReportOptions.BASIC}
          />

          <DependsOnField fieldName="reason">
            {(group: TReportOptions) =>
              group === OTHER.value ? (
                <TextField
                  required
                  columns={1}
                  label={t('Description')}
                  maxLength={254}
                  name="description"
                />
              ) : null
            }
          </DependsOnField>
        </EntityForm>
      </Popover>
    </div>
  );
}
