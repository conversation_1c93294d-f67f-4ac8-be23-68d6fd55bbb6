import React, { FC, useCallback } from 'react';
import { get } from 'lodash';

import useDimensionElements from '../../data/hooks/useDimensionElements';
import { useDependsOnFields } from '../containers/EntityForm';
import {
  IEntityFieldInputProps,
  IEntityFieldMeta,
} from '../containers/EntityForm/internal/EntityField';
import useEntityFormContext from '../containers/EntityForm/internal/useEntityFormContext';
import SpinnerError from '../utils/SpinnerError';
import TextEditor from './base/TextEditor';
import { IFroalaConfig, ITextEditorProps } from './base/TextEditor/TextEditor';
import useCurrentUser from '../../data/hooks/useCurrentUser';

const FormTextEditor: FC<IFormTextEditorProps> = ({
  input: { onChange, value },
  meta,
  isDisabled = false,
  errorMessage,
  name,
  hasMergeCodes = false,
  isRequired,
  moduleAreaKey,
  ...rest
}) => {
  const { submitting, error, touched, submitFailed } = meta;
  const { modulesDimensionElements, loading } = useDimensionElements({
    moduleAreaKey,
  });
  const {
    me,
    loading: currentUserLoading,
    error: currentUserError,
  } = useCurrentUser();
  const { setFieldValue } = useEntityFormContext();
  const { uploaded } = useDependsOnFields<{ uploaded: string[] }>({
    uploaded: `${name}Uploaded`,
  });

  const setLoading = useCallback(
    (isLoading: boolean) => {
      if (name) {
        setFieldValue(`${name}Loading`, isLoading || currentUserLoading);
      }
    },
    [name, setFieldValue, currentUserLoading],
  );

  const handleFileUpload = useCallback(
    (fileId: string) => {
      if (name) {
        setFieldValue(`${name}Uploaded`, [...(uploaded || []), fileId]);
      }
    },
    [name, setFieldValue, uploaded],
  );

  const currentLanguage = get(me, 'preferences.UI_LANGUAGE');

  return (
    <SpinnerError
      inline={false}
      loading={(hasMergeCodes && loading) || currentUserLoading}
    >
      <TextEditor
        errorMessage={
          ((touched || submitFailed) && (errorMessage || error)) || undefined
        }
        isDisabled={isDisabled || submitting}
        isRequired={isRequired}
        language={currentLanguage}
        mergeCodes={hasMergeCodes ? modulesDimensionElements : undefined}
        setLoading={setLoading}
        value={value}
        onChange={onChange}
        onFileUpload={handleFileUpload}
        {...rest}
      />
    </SpinnerError>
  );
};

export default FormTextEditor;

interface IFormTextEditorProps
  extends Pick<
    ITextEditorProps,
    | 'mergeCodes'
    | 'value'
    | 'animateTitle'
    | 'isExtended'
    | 'label'
    | 'isErrorMessageLiftedUp'
  > {
  input: Partial<Pick<IEntityFieldInputProps<string>, 'onChange' | 'value'>>;
  meta: Partial<IEntityFieldMeta>;
  name?: string;
  isRequired?: boolean;
  moduleAreaKey?: string;
  isDisabled?: boolean;
  hasMergeCodes?: boolean;
  config?: IFroalaConfig;
  errorMessage?: string;
  clobsPreviewData?: Record<string, any>;
  cookedAttributes?: any;
  hasRenderedPreview?: boolean;
}
