import classnames from 'classnames';
import uuid from 'uuid';

import 'froala-editor/css/froala_editor.pkgd.min.css';
import 'froala-editor/css/froala_style.min.css';
import 'froala-editor/js/plugins.pkgd.min.js';
import 'froala-editor/js/languages/ar.js';
import 'froala-editor/js/languages/zh_cn.js';
import 'froala-editor/js/languages/fr.js';
import 'froala-editor/js/languages/de.js';
import 'froala-editor/js/languages/it.js';
import 'froala-editor/js/languages/pt_pt.js';
import 'froala-editor/js/languages/ru.js';
import 'froala-editor/js/languages/es.js';

import React, {
  FC,
  ReactNode,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import FroalaEditorComponent from 'react-froala-wysiwyg';
import FroalaEditorView from 'react-froala-wysiwyg/FroalaEditorView';

import { IModule } from '../../../../abstract/IModuleProps';
import useAppConfig from '../../../utils/AppConfig/useAppConfig';
import FormControlErrorMessage from '../../../utils/FormControlErrorMessage';
import { TFormControlInfoMessageAlign } from '../../../utils/FormControlInfoMessage';
import useT from '../../../utils/Translations/useT';
import { LanguageType } from '../../../utils/Translations/helpers/languages';
import AnimatedTitle from '../AnimatedTitle';
import styles from './TextEditor.scss';
import TextEditorInfoMessages from './TextEditorInfoMessages';
import useEvents from './useEvents';
import useInitMergeCodesButton from './useInitMergeCodesButton';
import useToolbarButtons from './useToolbarButtons';
import { isRTL } from '../../../../utils/commonHelper';
import PreviewRenderer from '../PreviewRendering';

interface FroalaEditorInstance {
  editor: {
    events: {
      trigger: (event: string) => void;
    };
    html: {
      get: () => string;
      getSelected: () => string;
      set: (htmlString: string) => void;
      insert: (htmlString: string) => void;
    };
  };
}

const VIEW_LINE_HEIGHT = 20;

const TextEditor: FC<ITextEditorProps> = ({
  infoMessagesAlign,
  animateTitle,
  errorMessage,
  customLabel,
  infoMessages,
  hasMoreButton,
  isDisabled,
  value,
  label,
  isRequired,
  onChange,
  isExtended = false,
  mergeCodes,
  config = {},
  fileCategory,
  setLoading,
  onFileUpload,
  language = 'en',
  isErrorMessageLiftedUp = true,
  clobsPreviewData,
  cookedAttributes,
  hasRenderedPreview = false,
}) => {
  const editorRef = useRef<FroalaEditorInstance | undefined>(null);
  const [isFocus, setIsFocus] = useState(false);
  const [counter, setCounter] = useState(0);
  const resolvedLanguage = useMemo(() => resolveSupportedLanguage(language), [
    language,
  ]);

  const key = useMemo(() => `${resolvedLanguage}_${uuid.v4()}`, [
    resolvedLanguage,
  ]);

  const t = useT();
  const { froalaActivationKey } = useAppConfig();

  useInitMergeCodesButton(mergeCodes);

  const toolbarButtons = useToolbarButtons({
    mergeCodes,
    isExtended,
    fileCategory,
    hasMoreButton,
  });

  const events = useEvents({
    events: config?.events,
    setLoading,
    fileCategory,
    onFileUpload,
    setIsFocus,
    setCounter,
  });

  const getTextContent = (html: string): string => {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  };

  const checkForeignImages = async (htmlString: string): Promise<string> => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');

    async function walk(node: Node): Promise<void> {
      if (node.nodeType === Node.ELEMENT_NODE) {
        const children = Array.from(node.childNodes);
        for (const child of children) {
          await walk(child);
        }

        if ((node as HTMLElement).tagName === 'IMG') {
          const img = node as HTMLImageElement;
          const src = img.getAttribute('src');

          if (src && src.includes('googleusercontent')) {
            try {
              const response = await fetch(src);
              const blob = await response.blob();
              const blobUrl = URL.createObjectURL(blob);
              img.setAttribute('src', blobUrl);
            } catch (error) {
              img.remove();
            }
          }
        }
      }
    }

    await walk(doc.body);
    return new XMLSerializer().serializeToString(doc.body);
  };

  const formatAndTrimHTML = (htmlString: string, charLimit: number): string => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    doc.querySelectorAll('script, meta').forEach(el => el.remove());

    function trimNodes(node: Node): void {
      let totalChars = 0;

      const walk = node => {
        if (totalChars >= charLimit) {
          node.remove();
          return;
        }

        if (node.nodeType === Node.TEXT_NODE) {
          if (totalChars + node.textContent!.length > charLimit) {
            node.textContent = node.textContent!.substring(
              0,
              charLimit - totalChars,
            );
            totalChars = charLimit;
          } else {
            totalChars += node.textContent!.length;
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          Array.from(node.childNodes).forEach(walk);
          if (
            !node.hasChildNodes() &&
            node.nodeName !== 'IMG' &&
            node.textContent!.trim() === ''
          ) {
            node.remove();
          }
        }
      };

      walk(node);
    }

    trimNodes(doc.body);
    return new XMLSerializer().serializeToString(doc.body);
  };

  const removeStyleFromHTML = (htmlString: string): string => {
    const div = document.createElement('div');
    div.innerHTML = htmlString;
    div
      .querySelectorAll('[style]')
      .forEach(el => (el as HTMLElement).removeAttribute('style'));
    return div.innerHTML;
  };

  const replaceHtmlTag = (
    originalHtml: string,
    oldHtmlSnippet: string,
    newHtmlSnippet: string,
  ): string => {
    const parser = new DOMParser();
    const doc = parser.parseFromString(
      `<div>${originalHtml}</div>`,
      'text/html',
    );
    const container = doc.body.firstChild; // Initially just a ChildNode

    // Ensure container is an Element before proceeding
    if (container instanceof Element) {
      Array.from(container.querySelectorAll('*')).forEach(
        (element: Element) => {
          const elementHtml = new XMLSerializer().serializeToString(element);
          const normalizedElementHtml = elementHtml.replace(/"/g, "'");
          const normalizedOldHtmlSnippet = oldHtmlSnippet.replace(/"/g, "'");

          if (normalizedElementHtml === normalizedOldHtmlSnippet) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = newHtmlSnippet;
            const newElement = tempDiv.firstChild;
            if (newElement && newElement instanceof Node) {
              // Ensure newElement is a Node
              element.parentNode!.replaceChild(newElement, element);
            }
          }
        },
      );

      return container.innerHTML;
    }
    // If container is not an Element, return empty string or handle error
    console.error('Expected an Element but got a different type.');
    return '';
  };

  function trimAndConvertNewlines(inputString: string, maxLength: number) {
    // Track newline positions in the original string
    const newlinePositions: number[] = [];
    for (let i = 0, len = inputString.length; i < len; i++) {
      if (inputString[i] === '\n') {
        newlinePositions.push(i);
      }
    }

    // Trim string without considering newlines
    const trimmedString = inputString
      .replace(/\n/g, '')
      .substring(0, maxLength);

    // Create an array from the trimmed string
    const resultArray = Array.from(trimmedString);

    // Insert <br> based on the original newline positions, adjusting for the removed newlines
    newlinePositions.forEach(pos => {
      if (pos < maxLength) {
        resultArray.splice(pos, 0, '<br>');
      }
    });

    return resultArray.join('');
  }

  const pasteBeforeHandler = useCallback(async function (originalEvent) {
    originalEvent.stopPropagation();
    originalEvent.preventDefault();

    // eslint-disable-next-line no-magic-numbers
    const CHAR_LIMIT = config.charCounterMax || 100000;

    if (!editorRef.current || !editorRef.current.editor) return;

    const currentContent = getTextContent(editorRef.current.editor.html.get());
    const realContent = currentContent.replace('Powered by Froala Editor', '');
    const clipboardData = originalEvent.clipboardData;

    if (!clipboardData) return;

    const htmlText = clipboardData.getData('text/html');
    const plainText = clipboardData.getData('text/plain');
    let pastedData = htmlText || plainText;

    const regex = /<!--StartFragment-->(.*?)<!--EndFragment-->/s;
    const match = pastedData.match(regex);
    if (match && match[1]) {
      pastedData = match[1];
    }

    if (htmlText) {
      pastedData = await checkForeignImages(htmlText);
    }

    if (pastedData.length + realContent.length > CHAR_LIMIT) {
      pastedData = htmlText
        ? formatAndTrimHTML(htmlText, CHAR_LIMIT - realContent.length)
        : trimAndConvertNewlines(pastedData, CHAR_LIMIT - realContent.length);
    } else if (plainText) {
      pastedData = trimAndConvertNewlines(pastedData, pastedData.length + 1);
    }

    editorRef.current.editor.html.insert(pastedData);
    editorRef.current.editor.events?.trigger('blur');
    editorRef.current.editor.events?.trigger('focus');
    return false;
  }, []);

  const commandsAfterHandler = useCallback(function (cmd: string) {
    if (!editorRef.current || !editorRef.current.editor) return;

    if (cmd === 'paragraphFormat' || cmd === 'paragraphStyle') {
      const selected = editorRef.current.editor.html.getSelected();
      const afterRemoved = removeStyleFromHTML(selected);
      const currentContent = editorRef.current.editor.html.get();

      const modifiedHtml = replaceHtmlTag(
        currentContent,
        selected,
        afterRemoved,
      );
      const escapedSelected = selected.replace(
        /[-\/\\^$*+?.()|[\]{}]/g,
        '\\$&',
      );
      const regex = new RegExp(escapedSelected, 'g');
      const realContent = modifiedHtml.replace(regex, afterRemoved);

      editorRef.current.editor.html.set(realContent);
    }
  }, []);

  const extendedEvents = useMemo(
    async () => ({
      ...events,
      'paste.before': await pasteBeforeHandler,
      'commands.after': commandsAfterHandler,
    }),
    [events, pasteBeforeHandler, commandsAfterHandler],
  );

  const _config = useMemo(
    () => ({
      heightMin: 300,
      heightMax: 10000,
      toolbarButtons,
      placeholderText: t('Type something'),
      charCounterCount: true,
      toolbarBottom: false,
      immediateReactModelUpdate: false,
      quickInsertTags: [''],
      attribution: false,
      toolbarSticky: false,
      imageOutputSize: true,
      ...config,
      events: extendedEvents,
      key: froalaActivationKey,
      language: resolvedLanguage,
      imageEditButtons: ['imageDisplay', 'imageAlign', 'imageRemove'],
    }),
    [
      fileCategory,
      extendedEvents,
      froalaActivationKey,
      t,
      toolbarButtons,
      config,
      resolvedLanguage,
    ],
  );

  const _counterTitle = useMemo(() => {
    const { charCounterMax } = config;
    if (!charCounterMax) return;
    return `${counter}/${charCounterMax}`;
  }, [counter, config]);
  const rtlClass = useMemo(
    () => (isDisabled && isRTL(value) ? styles.rtl : ``),
    [value, isDisabled],
  );

  return (
    <div className="form-group mt-15 mb-0">
      {animateTitle && !customLabel && (
        <AnimatedTitle
          className={classnames(styles['text-editor-label'], {
            'form-group-material': animateTitle,
          })}
          isRequired={isRequired}
          placeholder={label}
          value={value}
        />
      )}
      {customLabel}
      <div
        className={classnames(styles['text-editor'], rtlClass, {
          [styles['editor-disabled']]: isDisabled,
          [styles['toolbar-hidden']]: hasMoreButton,
        })}
        // eslint-disable-next-line react/forbid-dom-props
        style={
          isDisabled
            ? {
                overflow: 'auto',
                marginBottom: '10px',
              }
            : {}
        }
      >
        {isDisabled ? (
          <>
            {hasRenderedPreview ? (
              <PreviewRenderer
                clobsPreviewData={clobsPreviewData}
                cookedAttributes={cookedAttributes}
                hasRenderedPreview={hasRenderedPreview}
                value={value}
              />
            ) : (
              <FroalaEditorView config={_config} model={value} />
            )}
          </>
        ) : (
          <>
            <FroalaEditorComponent
              key={key}
              ref={editorRef as any}
              config={_config}
              model={value}
              tag="textarea"
              onModelChange={onChange}
            />
            <span className={styles.customCounter}>
              {isFocus ? _counterTitle : ''}
            </span>
          </>
        )}
      </div>
      {infoMessages && !errorMessage && (
        <TextEditorInfoMessages
          infoMessages={infoMessages}
          infoMessagesAlign={infoMessagesAlign}
        />
      )}
      {errorMessage && (
        <FormControlErrorMessage
          isStatic
          className={isErrorMessageLiftedUp ? styles.liftedError : ''}
          errorMessage={errorMessage}
        />
      )}
    </div>
  );
};

export default TextEditor;

export interface ITextEditorProps {
  animateTitle?: boolean;
  errorMessage?: string;
  customLabel?: ReactNode;
  infoMessages?: Array<string> | string;
  hasMoreButton?: boolean;
  isDisabled?: boolean;
  value?: string;
  onChange?: (value?: string) => void;
  label?: string;
  isRequired?: boolean;
  infoMessagesAlign?: TFormControlInfoMessageAlign;
  isExtended?: boolean;
  mergeCodes?: Array<IModule>;
  config?: IFroalaConfig;
  fileCategory?: string;
  setLoading?: (isLoading: boolean) => void;
  onFileUpload?: (fileId: string) => void;
  language: LanguageType;
  isErrorMessageLiftedUp?: boolean;
  clobsPreviewData?: Record<string, any>;
  cookedAttributes?: any;
  hasRenderedPreview?: boolean;
}

export interface IFroalaConfig {
  events?: Record<string, Function>;
  toolbarButtons?: string[];
  quickInsertTags?: string[];
  toolbarBottom?: boolean;
  immediateReactModelUpdate?: boolean;
  placeholder?: string;
  placeholderText?: string;
  inlineStyles?: object;
  paragraphStyles?: object;
  height?: number;
  heightMin?: number;
  rows?: number;
  pastePlain?: boolean;
  charCounterMax?: number;
}

function resolveSupportedLanguage(language: LanguageType): string {
  const DEFAULT_LANGUAGE = 'en';
  const PORTUGUESE = 'pt';
  const FROALA_PORTUGUESE = 'pt_pt';
  const UNSUPPORTED_LANGUAGES = ['zh', 'pt'];

  if (language === DEFAULT_LANGUAGE) {
    return language;
  }

  if (!UNSUPPORTED_LANGUAGES.some(l => l === language)) {
    return language;
  }

  if (language === PORTUGUESE) {
    return FROALA_PORTUGUESE;
  }

  return DEFAULT_LANGUAGE;
}
