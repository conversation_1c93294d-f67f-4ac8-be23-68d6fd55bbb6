import classnames from 'classnames';
import { add, isEmpty, isNil, subtract, toNumber } from 'lodash';
import PropTypes from 'prop-types';
import React from 'react';

import EplusPropTypes from '../../../propTypes';
import BigTextAreaField from '../../containers/EntityForm/fields/BigTextAreaField';
import FormControlErrorMessage from '../../utils/FormControlErrorMessage';
import FormControlInfoMessage from '../../utils/FormControlInfoMessage';
import Icon from '../../utils/Icon';
import ModalWidget from '../../widgets/ModalWidget';
import AnimatedTitle from './AnimatedTitle';

import style from './NumberField.scss';

const ZERO_VALUE = 0;
const LOWER_CASE_E = 101;
const UPPER_CASE_E = 69;
const ADDITION_SYMBOL = 43;
const SUBTRACTION_SYMBOL = 45;
const DOT_SYMBOL = 46;
const MAXIMUM = 2000000000;

export default class NumberField extends React.PureComponent {
  static propTypes = {
    name: PropTypes.string,
    value: PropTypes.oneOfType([
      PropTypes.string.isRequired,
      PropTypes.number.isRequired,
    ]),
    className: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    onBlur: PropTypes.func,
    onPaste: PropTypes.func,
    onFocus: PropTypes.func,
    inputProps: PropTypes.object,
    disabled: PropTypes.bool,
    leftIcon: EplusPropTypes.icon,
    label: PropTypes.string,
    placeholder: PropTypes.string,
    errorMessage: EplusPropTypes.formErrorMessage,
    infoMessage: PropTypes.string,
    infoMessageAlign: PropTypes.oneOf(['left', 'right']),
    animateTitle: PropTypes.bool,
    formElement: PropTypes.bool,
    inputContainer: PropTypes.bool,
    noLabel: PropTypes.bool,
    description: PropTypes.bool,
    required: PropTypes.bool,
    renderHelpBlock: PropTypes.func,
    draggable: PropTypes.node,
    change: PropTypes.func,
    member: PropTypes.string,
    data: PropTypes.object,
    flex: PropTypes.bool,
    maxLength: PropTypes.number,
    max: PropTypes.number,
    min: PropTypes.number,
    refFunc: PropTypes.func,
    hasNegative: PropTypes.bool,
    step: PropTypes.number,
    precision: PropTypes.number,
    hasArrows: PropTypes.bool,
    inputGroupClass: PropTypes.string,
    errorClassName: PropTypes.string,
    errorIsStatic: PropTypes.bool,
    errorNoMargin: PropTypes.bool,
  };

  static defaultProps = {
    name: null,
    value: '',
    leftIcon: null,
    errorMessage: null,
    infoMessage: null,
    infoMessageAlign: 'left',
    label: null,
    placeholder: null,
    inputProps: {},
    disabled: false,
    animateTitle: false,
    formElement: true,
    required: false,
    onBlur: null,
    onPaste: null,
    onFocus: null,
    renderHelpBlock: null,
    className: null,
    noLabel: false,
    draggable: null,
    inputContainer: null,
    description: false,
    change: null,
    member: null,
    data: {},
    flex: false,
    maxLength: undefined,
    max: undefined,
    min: undefined,
    refFunc: null,
    hasNegative: false,
    step: 1,
    precision: 0,
    hasArrows: true,
    inputGroupClass: '',
    errorClassName: '',
    errorIsStatic: false,
    errorNoMargin: false,
  };

  state = { visible: false, maxError: null };

  handleModalToggle = () =>
    this.setState(state => ({ visible: !state.visible }));

  isValueOk = value => !isNil(value) && value !== '';

  renderAnimatedTitle() {
    const { value, placeholder, label, required } = this.props;

    return (
      <AnimatedTitle
        isRequired={required}
        placeholder={this.isValueOk(value) ? label : placeholder || label}
        value={this.isValueOk(value)}
      />
    );
  }

  renderErrorMessage() {
    const {
      errorMessage,
      errorClassName,
      errorIsStatic,
      errorNoMargin,
    } = this.props;
    return (
      <FormControlErrorMessage
        className={errorClassName}
        errorMessage={errorMessage}
        isStatic={errorIsStatic}
        noMargin={errorNoMargin}
      />
    );
  }

  renderInfoMessage() {
    const { infoMessage, infoMessageAlign } = this.props;

    return (
      <FormControlInfoMessage
        message={infoMessage}
        messageAlign={infoMessageAlign}
      />
    );
  }

  renderLeftIcon() {
    const { leftIcon } = this.props;

    return (
      <div className="form-control-feedback">
        <Icon className="text-muted" name={leftIcon} />
      </div>
    );
  }

  handleSubmit = values => {
    const { change, member } = this.props;

    values.forEach((value, key) => change(`${member}.${key}`, value));

    this.handleModalToggle();
  };

  renderDescription = () => {
    const { visible } = this.state;
    const { data } = this.props;
    return (
      <ModalWidget
        top
        entity={data}
        entityName="description"
        icon="file-text"
        title="Section Title"
        visible={visible}
        onCancel={this.handleModalToggle}
        onModalToggle={this.handleModalToggle}
        onSubmit={this.handleSubmit}
      >
        <BigTextAreaField />
      </ModalWidget>
    );
  };

  handleBlur = event => {
    const { onBlur } = this.props;
    onBlur && onBlur(event);
  };
  handlePaste = event => {
    const { onPaste, name } = this.props;
    onPaste && onPaste(event, name);
  };

  handleFocus = event => {
    const { onFocus } = this.props;
    onFocus && onFocus(event);
  };

  handleNegativeValue = value => {
    const { hasNegative } = this.props;

    if (!hasNegative && Number(value) < 0) {
      return false;
    }

    return true;
  };

  isValueInRange = value => {
    const { max, min } = this.props;
    const isGreaterThanMin = min === undefined || min <= value;
    const isLowerThanMax = max === undefined || max >= value;

    return isGreaterThanMin && isLowerThanMax;
  };

  isValueCanBeChanged = value => {
    const { disabled } = this.props;
    const _value = Number(value);
    return (
      !disabled &&
      this.isValueInRange(_value) &&
      this.handleNegativeValue(_value)
    );
  };

  handleChange = event => {
    const { onChange, max } = this.props;
    event.persist();

    const { value } = event.target;
    if (!this.handleNegativeValue(value)) {
      return false;
    }
    this.handleValidate(event);
  };

  bindInputToContext = inputRef => {
    if (!inputRef) {
      return;
    }

    const { refFunc } = this.props;
    refFunc && refFunc(inputRef);
  };

  resolveValue = (value, action) => {
    const { max, step, precision } = this.props;
    const number = toNumber(value);
    let updatedValue = action === 'subtract' ? subtract(number, step) : number;
    updatedValue = action === 'add' ? add(number, step) : updatedValue;
    if (!this.handleNegativeValue(updatedValue)) {
      return ZERO_VALUE;
    }
    if (max && updatedValue > max) {
      this.setState(state => ({
        maxError: max === MAXIMUM ? `Maximum 2 Billion` : `Max value is ${max}`,
      }));
    } else {
      this.setState(state => ({
        maxError: null,
      }));
    }
    if (updatedValue > max) {
      return max;
    }
    if (precision && updatedValue) {
      updatedValue = updatedValue.toFixed(precision);
    }
    return updatedValue;
  };

  handleIncreaseClick = () => {
    const { value, onChange } = this.props;

    const _value = this.resolveValue(value, 'add');

    if (this.isValueCanBeChanged(_value)) {
      onChange(Number(_value));
    }
  };

  handleDecreaseClick = () => {
    const { value, onChange } = this.props;
    const _value = this.resolveValue(value, 'subtract');

    if (this.isValueCanBeChanged(_value)) {
      onChange(Number(_value));
    }
  };

  handleValidate = event => {
    const { value } = event.target;
    const { onChange } = this.props;
    const _value = this.resolveValue(value, '');
    if (!_value || this.isValueInRange(Number(_value))) {
      if (value === '') {
        onChange(value);
      } else {
        onChange(Number(_value));
      }
    }
  };

  handleOnWheel = event => {
    event.target.blur();
    event.stopPropagation();
    setTimeout(() => {
      event.target.focus();
    }, 0);
  };

  handleKeyPress = event => {
    const { precision } = this.props;
    if (
      event.charCode === LOWER_CASE_E ||
      event.charCode === UPPER_CASE_E ||
      event.charCode === ADDITION_SYMBOL ||
      event.charCode === SUBTRACTION_SYMBOL ||
      (event.charCode === DOT_SYMBOL && !precision)
    ) {
      event.preventDefault();
    }
  };

  get inputField() {
    const {
      name,
      placeholder,
      value,
      inputProps,
      maxLength,
      disabled,
      required,
      renderHelpBlock,
      step,
      hasArrows,
      inputGroupClass,
    } = this.props;

    const { className: inputClassName, ...restInputProps } = inputProps;

    const _value = isNil(value) ? '' : value;

    let inputField = (
      <input
        ref={this.bindInputToContext}
        autoComplete="off"
        className={classnames('form-control', inputClassName, style.inputWidth)}
        disabled={disabled}
        maxLength={maxLength}
        name={name}
        placeholder={required ? `${placeholder} *` : placeholder}
        role="presentation"
        step={step}
        title={placeholder}
        type="number"
        value={_value}
        onBlur={this.handleBlur}
        onChange={this.handleChange}
        onFocus={this.handleFocus}
        onKeyPress={this.handleKeyPress}
        onPaste={this.handlePaste}
        onWheel={this.handleOnWheel}
        {...restInputProps}
      />
    );

    if (renderHelpBlock) {
      inputField = (
        <>
          {inputField}
          <span className="help-block pull-right">{renderHelpBlock()}</span>
        </>
      );
    }

    inputField = (
      <div
        className={classnames(
          'input-group',
          inputGroupClass,
          style.errorMessageMargin,
        )}
      >
        {inputField}
        {hasArrows ? (
          <span className={classnames('input-group-addon', style.buttonSpan)}>
            <a onClick={this.handleIncreaseClick}>
              <Icon name="icon-arrow-up5" />
            </a>
            <br />
            <a onClick={this.handleDecreaseClick}>
              <Icon name="icon-arrow-down5" />
            </a>
          </span>
        ) : null}
      </div>
    );

    return inputField;
  }

  render() {
    const {
      hasArrows,
      errorMessage,
      infoMessage,
      leftIcon,
      animateTitle,
      formElement,
      className,
      inputContainer,
      noLabel,
      draggable,
      description,
      flex,
      errorIsStatic,
      errorNoMargin,
      errorClassName,
    } = this.props;

    const inputWrapperStyle = classnames(className, {
      'form-group': formElement,
      'has-feedback': !!leftIcon,
      'has-feedback-left': !!leftIcon,
      'form-group-material': animateTitle,
      'input-container': inputContainer,
      'input-group': !!draggable,
    });

    return (
      <div
        className={classnames(inputWrapperStyle, style.hideLegacyButton, {
          [style.flex]: flex,
        })}
      >
        {animateTitle && !noLabel && this.renderAnimatedTitle()}
        {draggable}
        {this.inputField}
        {description && this.renderDescription()}
        {isEmpty(this.state.maxError) &&
          errorMessage &&
          this.renderErrorMessage()}
        {!isEmpty(this.state.maxError) && (
          <FormControlErrorMessage
            className={classnames(errorClassName, 'text-size-small', {
              'mt-10': !hasArrows,
            })}
            errorMessage={this.state.maxError}
            isStatic={errorIsStatic}
            noMargin={errorNoMargin}
          />
        )}
        {infoMessage && !errorMessage && this.renderInfoMessage()}
        {leftIcon && this.renderLeftIcon()}
      </div>
    );
  }
}
