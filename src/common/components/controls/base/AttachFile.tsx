import React, { FC, useCallback, useMemo, useState } from 'react';
import { IFileAttachmentTemp } from '../../../abstract/IFileAttachments';
import useCurrentUser from '../../../data/hooks/useCurrentUser';
import useFsClient from '../../../data/hooks/useFsClient';
import { TIcon } from '../../../propTypes';
import ImageRounded from '../../utils/ImageRounded';
import Modal from '../../utils/Modal';
import useT from '../../utils/Translations/useT';
import AvatarUploadModalContent from '../AvatarUpload/AvatarUploadModalContent';
import styles from './AttachFile.scss';

const AttachFile: FC<IAttachFileProps> = ({
  value,
  onChange,
  fileCategory,
  isEditable = true,
  defaultIcon,
}) => {
  const t = useT();

  const {
    me: { tenantId, organisationGroupId },
  } = useCurrentUser();

  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

  const handleModalOpen = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const handleModalClose = useCallback(() => {
    setIsModalVisible(false);
  }, []);

  const handleUpdate = useCallback<(file: IFileAttachmentTemp) => void>(
    file => {
      onChange && onChange(file);
      handleModalClose();
    },
    [onChange, handleModalClose],
  );

  const fsClient = useFsClient();

  const photoUrl = useMemo<string | undefined>(() => {
    if (value) {
      return fsClient.getFileUrl({
        fileId: value.fileId,
        tenantId,
        organisationGroupId,
        version: value.version,
      });
    }

    return undefined;
  }, [fsClient, value, tenantId, organisationGroupId]);

  return (
    <div className={styles.wrapper}>
      <ImageRounded
        fileName={value?.fileName}
        hoverIconName={defaultIcon ? 'camera' : undefined}
        iconName={defaultIcon ? defaultIcon : 'camera'}
        size="form"
        src={photoUrl}
        onClick={isEditable ? handleModalOpen : undefined}
      />
      {isEditable ? (
        <Modal
          title={t('Upload Photo')}
          visible={isModalVisible}
          onClose={handleModalClose}
        >
          <AvatarUploadModalContent
            fileCategory={fileCategory}
            fileId={value?.fileId}
            hasDescription={false}
            hasSubtitle={false}
            hasWebcam={false}
            organisationGroupId={organisationGroupId}
            tenantId={tenantId}
            onUpdateProfilePhoto={handleUpdate}
          />
        </Modal>
      ) : null}
    </div>
  );
};

export default AttachFile;

interface IAttachFileProps {
  value?: IFileAttachmentTemp;
  onChange?: (value?: IFileAttachmentTemp) => void;
  fileCategory: string;
  isEditable?: boolean;
  defaultIcon?: TIcon;
}
