/* eslint-disable react/forbid-dom-props */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import classnames from 'classnames';
import uuid from 'uuid';

import Icon from '../../utils/Icon';
import styles from './AudioFilePreview.scss';
import Spinner from '../../utils/Spinner';
import useT from '../../utils/Translations/useT';
import { useAudio } from './AudioContext';

export interface IAudioFilePreview {
  url: string;
  duration: number;
}

const TIMER = 100;
const THOUSAND = 1000;
const NAVIGATION_STEP_SECONDS = 15;

const AudioFilePreview: React.FC<IAudioFilePreview> = ({
  url,
  duration: fileDuration,
}) => {
  const t = useT();
  const id = useMemo(() => uuid.v4(), []);
  const audio = useMemo(() => new Audio(url), [url]);
  const { currentTime } = audio;
  const [error, setError] = useState<string | null>(null);
  const {
    currentAudioInstance,
    setCurrentAudioInstance,
    audioStateMap,
  } = useAudio();
  const checkIsFileExists = async url => {
    try {
      const response = await fetch(url);
      if (!response.ok) {
        setError(response.statusText);
      }
      // eslint-disable-next-line no-empty
    } catch {}
  };

  useEffect(() => {
    checkIsFileExists(url);
  }, []);

  const [duration, setDuration] = useState(0);

  useEffect(() => {
    if (!fileDuration || isNaN(fileDuration)) {
      const handleLoadedMetadata = () => {
        setDuration(audio.duration);
      };
      audio.addEventListener('loadedmetadata', handleLoadedMetadata);
      return () => {
        audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      };
    }
    return setDuration(fileDuration / THOUSAND);
  }, [audio, fileDuration]);

  const intervalRef = useRef<any>();
  const slider = useRef<any>();

  const [{ isPlaying, progress, isLoading }, setState] = useState({
    isPlaying: false,
    isLoading: true,
    progress: 0,
  });

  const setTrackProgress = useCallback(
    progress => setState(state => ({ ...state, progress })),
    [setState],
  );

  useEffect(() => {
    audioStateMap.delete(audio);
  }, [audio]);

  const play = useCallback(() => {
    if (currentAudioInstance && currentAudioInstance !== audio) {
      currentAudioInstance.pause();

      const previousSetState = audioStateMap.get(currentAudioInstance);
      if (previousSetState) {
        previousSetState(state => ({ ...state, isPlaying: false }));
      }
    }

    setCurrentAudioInstance(audio);

    if (!audioStateMap.has(audio)) {
      audioStateMap.set(audio, setState);
    }

    if (Math.ceil(progress) === Math.ceil(duration) && !isPlaying) {
      setCurrentTime([0]);
    }
    setState(state => ({ ...state, isPlaying: true }));
  }, [
    setState,
    progress,
    duration,
    isPlaying,
    audio,
    audioStateMap,
    currentAudioInstance,
    setCurrentAudioInstance,
  ]);

  const stop = useCallback(
    () => setState(state => ({ ...state, isPlaying: false })),
    [setState],
  );

  const { start, end } = useMemo(
    () => ({
      start: formatTime(progress),
      end: formatTime(duration),
    }),
    [duration, progress],
  );

  const startTimer = () => {
    // Clear any timers already running
    clearInterval(intervalRef.current);
    intervalRef.current = setInterval(() => {
      if (!audio.ended) {
        setTrackProgress(audio.currentTime);
      }
    }, TIMER);
  };

  const onChange = value => {
    setCurrentTime(value);
    startTimer();
  };

  const setCurrentTime = value => {
    // Clear any timers already running
    clearInterval(intervalRef.current);
    audio.currentTime = value[0];
    setTrackProgress(audio.currentTime);
  };

  useEffect(() => {
    const handleUserInteraction = () => {
      audio.load(); // Start loading audio

      window.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('mousemove', handleUserInteraction);
      window.removeEventListener('touchstart', handleUserInteraction);
    };

    window.addEventListener('scroll', handleUserInteraction, { once: true });
    window.addEventListener('mousemove', handleUserInteraction, { once: true });
    window.addEventListener('touchstart', handleUserInteraction, {
      once: true,
    });

    return () => {
      window.removeEventListener('scroll', handleUserInteraction);
      window.removeEventListener('mousemove', handleUserInteraction);
      window.removeEventListener('touchstart', handleUserInteraction);
    };
  }, [audio]);

  useEffect(() => {
    if (isPlaying) {
      audio.play();
      startTimer();
    } else {
      audio.pause();
      clearInterval(intervalRef.current);
    }
  }, [isPlaying]);

  useEffect(() => {
    function handleDataLoad() {
      setState(state => ({ ...state, isLoading: false }));
    }

    audio.addEventListener('canplaythrough', handleDataLoad);

    return () => {
      audio.pause();
      clearInterval(intervalRef.current);
      audio.removeEventListener('canplaythrough', handleDataLoad);
    };
  }, []);

  useEffect(() => {
    if (!slider || !audio || !duration) {
      return;
    }

    noUiSlider.create(slider.current, {
      connect: [true, false],
      start: [0],
      range: {
        min: 0,
        max: duration,
      },
    });

    slider.current.noUiSlider.on('change', onChange);
    slider.current.noUiSlider.on('slide', () =>
      clearInterval(intervalRef.current),
    );

    return () => slider.current?.noUiSlider.destroy();
  }, [slider, audio, duration]);

  useEffect(() => {
    slider.current?.noUiSlider?.set(progress);
  }, [progress]);

  useEffect(() => {
    if (Math.ceil(duration) <= Math.ceil(currentTime)) {
      setTrackProgress(duration);
      stop();
    }
  }, [duration, currentTime]);

  const handleBackward = useCallback(() => {
    let upcomingProgress = progress - NAVIGATION_STEP_SECONDS;
    upcomingProgress = upcomingProgress < 0 ? 0 : upcomingProgress;

    if (isPlaying) {
      return onChange([upcomingProgress]);
    }

    setCurrentTime([upcomingProgress]);
  }, [onChange, progress]);

  const handleForward = useCallback(() => {
    let upcomingProgress = progress + NAVIGATION_STEP_SECONDS;
    upcomingProgress =
      upcomingProgress > duration ? duration : upcomingProgress;

    if (isPlaying) {
      return onChange([upcomingProgress]);
    }

    setCurrentTime([upcomingProgress]);
  }, [onChange, duration, progress]);

  if (error) {
    return (
      <p
        className={classnames(
          'text-danger text-center text-size-small',
          styles.alignContentCenter,
        )}
      >
        {t(error)}
      </p>
    );
  }
  return (
    <div className="text-center">
      <div className={styles['audio-controls-container']}>
        {isLoading && <Spinner className="display-inline-block" />}

        {!isLoading && [
          <Icon
            key={`backward-${id}`}
            className={classnames(
              styles['audio-control'],
              styles['secondary-audio-control'],
              'mr-20',
            )}
            name="backward2"
            onClick={handleBackward}
          />,
          isPlaying ? (
            <Icon
              key={`stop-${id}`}
              className={classnames(styles['audio-control'])}
              name="stop2"
              onClick={stop}
            />
          ) : (
            <Icon
              key={`play-${id}`}
              className={styles['audio-control']}
              name="play4"
              onClick={play}
            />
          ),
          <Icon
            key={`forward-${id}`}
            className={classnames(
              styles['audio-control'],
              styles['secondary-audio-control'],
              'ml-20',
            )}
            name="forward3"
            onClick={handleForward}
          />,
        ]}
      </div>

      <div
        className={classnames(styles['audio-progress-container'], {
          invisible: isLoading,
        })}
      >
        <div
          ref={slider}
          className={classnames(
            'noui-slider-info has-pips noUi-target noUi-ltr noUi-horizontal',
            styles['audio-progress-preview'],
          )}
        />
        <div
          className={classnames(
            'pull-left text-muted',
            styles['audio-progress-counter'],
          )}
        >
          {start}
        </div>
        <div
          className={classnames(
            'pull-right text-muted',
            styles['audio-progress-counter'],
          )}
        >
          {end}
        </div>
      </div>
    </div>
  );
};

function formatTime(time = 0): string {
  const secondsInMinutes = 60;
  const ten = 10;

  const isNegative = time < 0;

  const minutes = Math.floor(Math.abs(time) / secondsInMinutes);
  const seconds = Math.floor(Math.abs(time) % secondsInMinutes);

  const secondsWithLeadingZero = seconds < ten ? `0${seconds}` : seconds;
  const minutesWithLeadingZero = minutes < ten ? `0${minutes}` : minutes;

  return `${
    isNegative ? '-' : ''
  } ${minutesWithLeadingZero}:${secondsWithLeadingZero}`;
}

export default AudioFilePreview;
