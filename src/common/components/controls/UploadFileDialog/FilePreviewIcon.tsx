import React, { useCallback, useMemo } from 'react';
import classNames from 'classnames';

import { ALL_DOCUMENTS, WORD } from '../../../propTypes';
import { IFileAttachmentTemp } from '../../../abstract/IFileAttachments';

import styles from './FilePreviewIcon.scss';
import Icon from '../../utils/Icon';
import { loadable } from '../../../utils/lazyLoading';
import VideoFilePreview from './VideoFilePreview';
import AudioFilePreview from './AudioFilePreview';

const LIMIT = 5;
const KB = 1024;
const FIVE_MB = LIMIT * KB * KB;

const AttachmentPdfPreview = loadable<{ file?: string; onClick?: () => void }>(
  () =>
    import(
      '../../../../common/components/containers/EntityForm/fields/AttachmentsWithPreviewField/AttachmentPdfPreview'
    ),
);

const FilePreviewIcon: React.FC<{
  file: IFileAttachmentTemp;
  onDownload?: () => void;
}> = ({ file, onDownload }) => {
  const { fileSize, preview, url, mimeType, description } = file;

  const fileUrl = useMemo(() => preview || url, [preview, url]);

  const onImageError = useCallback(e => {
    if (!e) {
      return;
    }
    e.target.onerror = null;
    e.target.src = 'images/assignment.png';
  }, []);

  const { isEmbed, isPdf, isWord, isVideo, isAudio, isImage } = useMemo(() => {
    const isSvg =
      mimeType && mimeType.indexOf('svg') > -1 && fileSize < FIVE_MB;

    const isEmbed =
      mimeType &&
      mimeType.indexOf('image') > -1 &&
      fileSize < FIVE_MB &&
      !isSvg;

    const isPdf =
      mimeType && mimeType.indexOf('pdf') > -1 && fileSize < FIVE_MB;
    const isVideo =
      mimeType?.indexOf('video') > -1 || mimeType?.indexOf('flash') > -1;
    const isAudio = mimeType?.indexOf('audio') > -1;
    const isDocument = ALL_DOCUMENTS.some(x => x === mimeType);
    const isWord = WORD.some(x => x === mimeType);

    const isImage = !isVideo && !isAudio && !isPdf && !isDocument;

    return { isEmbed, isPdf, isWord, isVideo, isAudio, isImage, isDocument };
  }, [fileSize, mimeType]);

  if (isPdf) {
    return <AttachmentPdfPreview file={file.url} onClick={onDownload} />;
  }
  if (isImage) {
    return (
      <img
        alt={description}
        className="img-thumbnail cursor-pointer"
        src={fileUrl}
        onClick={onDownload}
        onError={onImageError}
      />
    );
  }

  if (isEmbed) {
    return (
      <embed
        className={classNames(styles.embed, 'center-block', 'cursor-pointer')}
        src={fileUrl}
        type={mimeType}
        onClick={onDownload}
      />
    );
  }

  if (isVideo) {
    return (
      <VideoFilePreview
        title={file.fileName as string}
        url={file.url as string}
      />
    );
  }

  if (isAudio) {
    return (
      <AudioFilePreview
        duration={file.duration as number}
        url={file.url as string}
      />
    );
  }

  if (isWord) {
    return (
      <Icon
        className="mediaIcon media-icon cursor-pointer"
        name="file-word"
        onClick={onDownload}
      />
    );
  }

  return (
    <Icon
      className={classNames(styles.flex, 'mediaIcon media-icon cursor-pointer')}
      name="file-empty"
      onClick={onDownload}
    />
  );
};

export default FilePreviewIcon;
