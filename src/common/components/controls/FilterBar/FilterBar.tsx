import { defaults, forEach, head, isArray, isEmpty, reduce } from 'lodash';
import React, { FC, Fragment, PureComponent } from 'react';
import classnames from 'classnames';

import AddButton from './AddButton';
import AssetCategorySelector from './AssetCategorySelector';
import AssetConditionsSelector from './AssetConditionsSelector';
import AssetGroupSelector from './AssetGroupSelector';
import AssetRegisterTreeViewSelector from './AssetRegisterTreeViewSelector';
import ButtonSelector from './ButtonSelector';
import CommunicateSource from './CommunicateSource';
import CommunicationLogType from './CommunicationLogType';
import CommunicationStatus from './CommunicationStatus';
import CommunicationType from './CommunicationType';
import ConfidentialityLevelSelector from './ConfidentialityLevelSelector';
import CustomizeColumn from './CustomizeColumn';
import DateRangePicker from './DateRangePicker';
import styles from './FilterBar.scss';
import GqlStaticTreeSelector from './GqlStaticTreeSelector';
import withFilterHiding from './hocs/withFilterHiding';
import withFilterStore from './hocs/withFilterStore';
import { ApplyFilterContext } from './hooks/useApplyFilter';
import IntakeOrganisationLevelsTreeSelector from './IntakeOrganisationLevelsTreeSelector';
import LearningPlanStatusSelector from './LearningPlanStatusSelector';
import LearningSpaceTasksTypeSelector from './LearningSpaceTasksTypeSelector';
import MultiselectFilter from './MultiselectFilter';
import OrganisationGroupSelector from './OrganisationGroupSelector';
import OrganisationsBeTreeSelector from './OrganisationsBeTreeSelector';
import OrganisationsTreeSelector from './OrganisationsTreeSelector';
import OrganisationStructuresSelector from './OrganisationStructuresSelector';
import OrgGroupPersonEntityTypeSelector from './OrgGroupPersonEntityTypeSelector';
import OrgHolidayCalendarsSelector from './OrgHolidayCalendarsSelector';
import ProgramGroupSelector from './ProgramGroupSelector';
import LibrarySelector from './LibrarySelector';
import ProgramIntakeClassStatusSelector from './ProgramIntakeClassStatusSelector';
import ProgramIntakeEnrolmentStatusSelector from './ProgramIntakeEnrolmentStatusSelector';
import ProgramStructureTreeSelector from './ProgramStructureTreeSelector';
import ProgramStructureLPTreeSelector from './ProgramStructureLPTreeSelector';
import ProgramTreeSelector from './ProgramTreeSelector';
import LPViewSelector from './LPViewSelector';
import ReportCategoryTreeSelector from './ReportCategoryTreeSelector';
import ScheduleProgramSelector from './ScheduleProgramSelector';
import SelectBoxFilter from './SelectBoxFilter';
import StatusSelector from './StatusSelector';
import StatusSelectorWithAll from './StatusSelectorWithAll';
import StatusWithDraftMultiSelector from './StatusWithDraftMultiSelector';
import StatusWithDraftAndActiveMultiSelector from './StatusWithDraftAndActiveMultiSelector';
import BusinessProcessStatusMultiSelector from './BusinessProcessStatusMultiSelector';
import StatusWithDraftSelector from './StatusWithDraftSelector';
import TenantInstitutionTypeSelector from './TenantInstitutionTypeSelector';
import TenantPlatformSelector from './TenantPlatformSelector';
import TenantStatusSelector from './TenantStatusSelector';
import ToggleAddon from './ToggleAddon';
import TreeSelector from './TreeSelector';
import YearsSelector from './YearsSelector';
import EContentLibraryResourcesTreeSelector from './EContentLibraryResourcesTreeSelector';
import EContentLibraryResourcesSingleSelector from './EContentLibraryResourcesSingleSelector';
import LanguagesMultiSelector from './LanguagesMultiSelector';
import IntakesListSelector from './IntakesListSelector';
import GroupPeopleBySelector from './GroupPeopleBySelector';
import withPaginationRouting from '../../../data/withPaginationRouting';
import { IUsePaginationRoutingResult } from '../../../data/hooks/usePaginationRouting';
import ContentViewSelector from './ContentViewSelector';
import ContentSortingSelector from './ContentSortingSelector';
import PersonEntityRoleSelectorWithAll from './PersonEntityRoleSelectorWithAll';
import ApplyFilterButton from '../ApplyFilterButton';
import ModuleSelector from './ModuleSelector';
import EContentLogSelector from './EContentLogSelector';
import CurriculumStructureViewSelector from './CurriculumStructureViewSelector';

export interface IFilterBarComponentProps<V> {
  name: string;
  value?: V;
  onChange?: (value?: V) => void;
  filter?: object;
  disabled?: boolean;
}

type TChild =
  | React.ReactElement<IFilterBarComponentProps<any>, any>
  | FC<IFilterBarComponentProps<any>>
  | null
  | TChild[];

@withFilterStore
@withFilterHiding
@withPaginationRouting
export default class FilterBar<T extends object> extends PureComponent<
  IFilterBarProps<T>,
  T
> {
  static defaultProps = {
    onChange: undefined,
    searchName: undefined,
    values: {},
    filterGroups: [],
    hasPaginationRouting: false,
  };

  constructor(props) {
    super(props);

    const { searchName, values } = props;

    this.state = Object.assign(
      {
        hovered: false,
      },
      searchName && values && values.hasOwnProperty(searchName)
        ? { [searchName]: values[searchName] }
        : {},
      // Share initial state from values for all filter items
      reduce(
        values,
        (acc, value, key) => {
          acc[key] = value;
          return acc;
        },
        {},
      ) as T,
    );
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const { searchName, values, onChange } = nextProps;
    if (!searchName || !values || !onChange) {
      return null;
    }

    const prevSearch = prevState[searchName];
    const nextSearch = values[searchName];

    if (prevSearch !== nextSearch) {
      const newState = {
        ...defaults(prevState, values),
        [searchName]: nextSearch,
      };
      onChange(newState);
      return newState;
    }

    return null;
  }

  componentDidUpdate({ values: valuesPrev = {} as T }: { values?: T }) {
    const { values = {}, searchName } = this.props;

    const propsToIterate = isEmpty(this.state) ? values : this.state;

    forEach(propsToIterate, (value, key) => {
      if (key !== searchName && valuesPrev[key] !== values[key]) {
        this.setState(({ [key]: values[key] } as unknown) as T);
      }
    });
  }

  static AddButton = AddButton;
  static SelectBoxFilter = SelectBoxFilter;
  static Multiselect = MultiselectFilter;
  static ButtonSelector = ButtonSelector;
  static StatusSelector = StatusSelector;
  static StatusSelectorWithAll = StatusSelectorWithAll;
  static PersonEntityRoleSelectorWithAll = PersonEntityRoleSelectorWithAll;
  static TenantStatusSelector = TenantStatusSelector;
  static LearningPlanStatusSelector = LearningPlanStatusSelector;
  static TenantPlatformSelector = TenantPlatformSelector;
  static TenantInstitutionTypeSelector = TenantInstitutionTypeSelector;
  static TreeSelector = TreeSelector;
  static GqlStaticTreeSelector = GqlStaticTreeSelector;
  static DateRangePicker = DateRangePicker;
  static CustomizeColumn = CustomizeColumn;
  static ToggleAddon = ToggleAddon;
  static ConfidentialityLevelSelector = ConfidentialityLevelSelector;
  static ReportCategoryTreeSelector = ReportCategoryTreeSelector;
  static OrganisationGroupSelector = OrganisationGroupSelector;
  static OrgGroupPersonEntityTypeSelector = OrgGroupPersonEntityTypeSelector;
  static OrganisationStructuresSelector = OrganisationStructuresSelector;
  static ProgramTreeSelector = ProgramTreeSelector;
  static LPViewSelector = LPViewSelector;
  static ProgramIntakeEnrolmentStatusSelector = ProgramIntakeEnrolmentStatusSelector;
  static ProgramIntakeClassStatusSelector = ProgramIntakeClassStatusSelector;
  static OrganisationsTreeSelector = OrganisationsTreeSelector;
  static OrganisationsBeTreeSelector = OrganisationsBeTreeSelector;
  static ProgramStructureTreeSelector = ProgramStructureTreeSelector;
  static ProgramStructureLPTreeSelector = ProgramStructureLPTreeSelector;
  static IntakeOrganisationLevelsTreeSelector = IntakeOrganisationLevelsTreeSelector;
  static LearningSpaceTasksTypeSelector = LearningSpaceTasksTypeSelector;
  static StatusWithDraftSelector = StatusWithDraftSelector;
  static StatusWithDraftMultiSelector = StatusWithDraftMultiSelector;
  static StatusWithDraftAndActiveMultiSelector = StatusWithDraftAndActiveMultiSelector;
  static BusinessProcessStatusMultiSelector = BusinessProcessStatusMultiSelector;
  static YearsSelector = YearsSelector;
  static OrgHolidayCalendarsSelector = OrgHolidayCalendarsSelector;
  static AssetConditionsSelector = AssetConditionsSelector;
  static AssetRegisterTreeViewSelector = AssetRegisterTreeViewSelector;
  static AssetGroupSelector = AssetGroupSelector;
  static AssetCategorySelector = AssetCategorySelector;
  static ScheduleProgramSelector = ScheduleProgramSelector;
  static ProgramGroupSelector = ProgramGroupSelector;
  static LibrarySelector = LibrarySelector;
  static CommunicateSource = CommunicateSource;
  static CommunicationType = CommunicationType;
  static CommunicationStatus = CommunicationStatus;
  static CommunicationLogType = CommunicationLogType;
  static EContentLibraryResourcesTreeSelector = EContentLibraryResourcesTreeSelector;
  static EContentLibraryResourcesSingleSelector = EContentLibraryResourcesSingleSelector;
  static LanguagesMultiSelector = LanguagesMultiSelector;
  static IntakesListSelector = IntakesListSelector;
  static GroupPeopleBySelector = GroupPeopleBySelector;
  static ContentViewSelector = ContentViewSelector;
  static ContentSortingSelector = ContentSortingSelector;
  static ModuleSelector = ModuleSelector;
  static EContentLogSelector = EContentLogSelector;
  static CurriculumStructureViewSelector = CurriculumStructureViewSelector;

  get isBatch() {
    return !!this.props.onChange;
  }

  handleChange = ({ name, onChange, mapValue }) => (...values) => {
    const { onFilterChanged } = this.props;
    if (!name || !this.isBatch) {
      return onChange(...values);
    }
    let value = mapValue ? mapValue(...values) : head(values);
    if (name === 'organisationStructureId' && values.length === 1) {
      const preservedValue = localStorage.getItem('organisationId');
      value =
        preservedValue !== null && preservedValue !== undefined
          ? Number(localStorage.getItem('organisationId'))
          : head(values);
    }
    if (onFilterChanged) {
      return this.setState(onFilterChanged({ [name]: value } as T, name));
    }
    this.setState({ [name]: value } as T);
  };

  handleApplyFilters = () => {
    const { onChange } = this.props;
    onChange && onChange({ ...this.state, first: 0 });
  };

  modifyGroup = child =>
    child && this.isBatch
      ? React.cloneElement(child, {
          onChange: this.handleChange,
          values: this.state,
          modifyChild: this.modifyChild,
        })
      : child;

  getValue = ({ name, value }) => {
    if (this.state.hasOwnProperty(name)) {
      return this.state[name];
    } else if (this.props.values?.hasOwnProperty(name)) {
      return this.props.values[name];
    }
    return value;
  };

  modifyChild = child =>
    child && this.isBatch
      ? React.cloneElement(child, {
          onChange: this.handleChange(child.props),
          value: this.getValue(child.props),
          filter: { ...child.props.filter, ...this.state },
        })
      : child;

  handleClickSearchButton = () => {
    const { hasPaginationRouting, setPageNumber } = this.props;
    this.handleApplyFilters();
    hasPaginationRouting && setPageNumber && setPageNumber(1);
  };

  handleMouseEnter = () => {
    this.setState({ hovered: true } as T);
  };
  handleMouseLeave = () => {
    this.setState({ hovered: false } as T);
  };

  renderSearchButton = () =>
    this.isBatch && (
      <ApplyFilterButton
        className={styles.searchButton}
        onClick={this.handleClickSearchButton}
      />
    );

  get children() {
    const { children } = this.props;
    if (!this.isBatch) {
      return children;
    }

    if (children && typeof children === 'function') {
      const child = (children as Function)({
        ...this.props.values,
        ...this.state,
      });
      return child.type === Fragment ? child.props.children : child;
    }

    return children;
  }

  splitFilters = children => {
    const { filterGroups } = this.props;
    if (isEmpty(filterGroups)) {
      return [isArray(children) ? children : [children]];
    }

    const filterGroupMap = reduce(
      filterGroups,
      (acc, v, idx) => {
        v.names.forEach(name => {
          acc[name] = idx + 1;
        });
        return acc;
      },
      {},
    );
    return reduce(
      children,
      (acc: string[][], child) => {
        if (child) {
          const idx = filterGroupMap[child.props.name] || 0;
          acc[idx] = acc[idx] || [];
          acc[idx].push(child);
        }
        return acc;
      },
      [],
    );
  };
  handleScrollBottom = () => {
    setTimeout(() => {
      const rightPanel: any = document.getElementsByClassName(
        'disableScrollbar',
      );
      if (rightPanel[0]) rightPanel[0].style.overflow = null;
      if (rightPanel[1]) rightPanel[1].style.overflow = null;
    }, 0);
  };
  render() {
    const groups = this.splitFilters(this.children);

    const { isVisible } = this.props;

    return (
      <div
        className={classnames(
          'navbar navbar-default navbar-xs navbar-component',
          { [styles.hidden]: !isVisible },
        )}
        onClick={this.handleScrollBottom}
      >
        <div
          className={`navbar-collapse collapse in ${styles.navbarAdjustment}`}
        >
          <ApplyFilterContext.Provider
            value={{ applyFilters: this.handleApplyFilters }}
          >
            {groups.map((filters, idx) => {
              const isLastGroup = idx === groups.length - 1;
              const key = filters
                .filter(f => !!f)
                .map(({ props: { name } }) => name)
                .join('-');

              return (
                <ul key={key} className="nav navbar-nav">
                  {React.Children.map(filters, child => {
                    if (!child) {
                      return null;
                    }

                    if (child.type.name === 'ToggleAddon') {
                      return null;
                    }

                    return (
                      <li
                        key={child.props?.name}
                        className={styles.filterBarItem}
                      >
                        {this.modifyChild(child)}
                      </li>
                    );
                  })}
                  {isLastGroup && this.renderSearchButton()}
                </ul>
              );
            })}
          </ApplyFilterContext.Provider>
        </div>
      </div>
    );
  }
}

export interface IFilterBarProps<T extends object>
  extends Partial<IUsePaginationRoutingResult> {
  children: TChild;
  onChange?: (values: T) => void;
  searchName?: string;
  values?: T;
  filterGroups?: {
    names: string[];
  }[];
  filterKey?: string;
  isVisible?: boolean;
  isHidingEnabled?: boolean;
  onFilterChanged?: (value: T, filterName: string) => T;
  hasPaginationRouting?: boolean;
  onBeforeUpdate?: (values: Partial<T>) => Partial<T>;
  onBeforeComplete?: (values: Partial<T>) => Partial<T>;
}
