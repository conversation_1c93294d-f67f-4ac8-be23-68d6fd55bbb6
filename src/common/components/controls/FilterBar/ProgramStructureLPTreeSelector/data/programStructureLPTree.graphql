#import './ProgramFragmentShort.graphql'
#import './ProgramStructureSubjectFragment.graphql'
#import './LearningPlanFragment.graphql'
#import './LearningPlanCategoryFragment.graphql'
#import './LearningPlanTaskFragment.graphql'

query programStructureLPTree($programGroupId: Int!, $view: String!, $withTaskIds: Boolean, $programStructureLPTaskIds: [Int]) {
  programStructureLPTree(programGroupId: $programGroupId, view: $view, withTaskIds: $withTaskIds, programStructureLPTaskIds: $programStructureLPTaskIds) {
    ... on ProgramFolder {
      id
      name
      status
      parentId
      sequence
    }

    ... on Program {
      ...ProgramFragmentShort
    }

    ... on ProgramStructureSubject {
      ...ProgramStructureSubjectFragment
      subjectId
    }

    ... on LearningPlan {
      ...LearningPlanFragment
    }

    ... on LearningPlanCategory {
      ...LearningPlanCategoryMain
    }

    ... on LearningPlanTask {
      ...LearningPlanTaskMain
    }
  }
}
