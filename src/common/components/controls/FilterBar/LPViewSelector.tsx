import React from 'react';

import useT from '../../utils/Translations/useT';
import SelectBoxFilter from './SelectBoxFilter';

const LPViewSelector = ({ activityNamePlural, ...props }) => {
  const t = useT();
  const title = t('View');

  const options = [
    {
      value: 'ALL_ACTIVE_SUBJECTS',
      name: `All Active ${activityNamePlural}`,
    },
    {
      value: 'SUBJECTS_WITH_LP',
      name: `${activityNamePlural} with Learning Plan`,
    },
  ];

  return (
    <SelectBoxFilter name="view" options={options} title={title} {...props} />
  );
};

export default LPViewSelector;
