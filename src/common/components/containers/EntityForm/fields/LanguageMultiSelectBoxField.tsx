import { find, flatten, isArray, join, map, isEmpty } from 'lodash';
import React, { useCallback, useState } from 'react';
import classNames from 'classnames';
import {
  ITEM_TITLE_PROP_NAME,
  ITEM_VALUE_PROP_NAME,
} from '../../../controls/base/Multiselect';
import FormMultiselect from '../../../controls/FormMultiselect';
import ValueView from '../../../controls/ValueViews/ValueView';

import { useTranslation } from '../../../utils/Translations';

import EntityField from '../internal/EntityField';
import TableCellTooltip from '../../../../../common/components/utils/TableCellToolTip';
import styles from './LanguageMultiSelectBoxField.scss';

const MAX_VISIBLE_ITEMS = 2;
export interface ILanguageMultiSelectBoxField {
  name: string;
  label: string;
  isRequired?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value?: Array<any>;
  onChange?: Function;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: Array<any>;
  groupTitleKeys?: Array<string>;
  validate?: Function;
  itemTitlePropName?: string;
  itemValuePropName?: string;
  itemRenderer?: Function;
  className?: string;
  columns?: number;
  required?: boolean;
  disabled?: boolean;
  loading?: boolean;
  isLiveSearch?: boolean;
  orderOptionsBy?: string | string[];
  defaultValue?: Array<any>;
}
const LanguageMultiSelectBoxField: React.FC<ILanguageMultiSelectBoxField> = ({
  itemTitlePropName = ITEM_TITLE_PROP_NAME,
  itemValuePropName = ITEM_VALUE_PROP_NAME,
  options = [],
  groupTitleKeys,
  isRequired = false,
  validate = null,
  itemRenderer,
  className = '',
  name,
  label,
  ...rest
}) => {
  const { t } = useTranslation();
  const [isAddonTooltipOpen, setIsAddonTooltipOpen] = useState(false);

  const concatValue = useCallback(
    ({ input: { value } }) => {
      const flatOptions = flatten(options);

      return isArray(value)
        ? join(
            map(value, item => {
              const founded = find(flatOptions, { [itemValuePropName]: item });

              return founded ? founded[itemTitlePropName] : '';
            }),
            ', ',
          )
        : value;
    },
    [itemTitlePropName, itemValuePropName, options],
  );

  const validator = useCallback(
    fields => {
      if (isRequired && (!fields || fields.length === 0)) {
        return 'Required';
      }

      if (validate) {
        const error = validate(name);
        if (error) {
          return error;
        }
      }
    },
    [isRequired, name, validate],
  );

  const renderTooltipLine = useCallback(name => <p>{name}</p>, []);

  const handleAddonTooltipOpen = useCallback(() => {
    setIsAddonTooltipOpen(true);
  }, [setIsAddonTooltipOpen]);

  const handleAddonTooltipClose = useCallback(() => {
    setIsAddonTooltipOpen(false);
  }, [setIsAddonTooltipOpen]);

  const renderAddon = useCallback(
    (value: string) => {
      const languages = value.split(',');
      // eslint-disable-next-line react/display-name, react/no-multi-comp
      return () => (
        <TableCellTooltip
          lines={languages}
          minLineLength={2}
          trigger="hover"
          onClose={handleAddonTooltipClose}
          onOpen={handleAddonTooltipOpen}
        >
          {renderTooltipLine}
        </TableCellTooltip>
      );
    },
    [renderTooltipLine, handleAddonTooltipOpen, handleAddonTooltipClose],
  );

  // eslint-disable-next-line react/no-multi-comp
  const renderTooltip = (value: string) => {
    const languages = value.split(',');
    // eslint-disable-next-line react/display-name, react/no-multi-comp
    return (
      <TableCellTooltip lines={languages} minLineLength={2} trigger="hover">
        {renderTooltipLine}
      </TableCellTooltip>
    );
  };

  const renderTitle = (value: string) => {
    const languages = value.split(',');
    if (!languages[0]) return t('None');
    const title = [languages[0]];

    if (languages.length > 1) {
      let i = 1;
      while (languages[i] && i < MAX_VISIBLE_ITEMS) {
        title.push(languages[i]);
        i++;
      }
    }

    if (languages.length > MAX_VISIBLE_ITEMS) {
      title.push('...');
    }
    return title.join(', ');
  };

  return (
    <EntityField
      {...rest}
      name={name}
      required={isRequired}
      validate={validator}
    >
      {(editing, field, { required, disabled }) => {
        const value = concatValue(field);

        if (editing) {
          return (
            <div className="input-group w-100">
              <FormMultiselect
                className={className}
                disabled={disabled || field?.meta?.submitting}
                groupTitleKeys={groupTitleKeys}
                itemRenderer={itemRenderer}
                itemTitlePropName={itemTitlePropName}
                itemValuePropName={itemValuePropName}
                maxNumberToShow={MAX_VISIBLE_ITEMS}
                options={options}
                placeholder={label}
                required={required}
                {...field}
              />
              <span
                className={classNames(
                  styles.tooltipContainer,
                  'input-group-addon no-padding ',
                )}
              >
                {renderTooltip(value)}
              </span>
            </div>
          );
        }

        return (
          <ValueView
            label={label}
            renderAddon={renderAddon(value)}
            title={isAddonTooltipOpen ? '' : value}
            value={isEmpty(value) ? t('None') : renderTitle(value)}
          />
        );
      }}
    </EntityField>
  );
};

export default LanguageMultiSelectBoxField;
