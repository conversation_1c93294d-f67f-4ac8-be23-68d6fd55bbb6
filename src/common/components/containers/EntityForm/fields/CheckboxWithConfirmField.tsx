/* global swal */
import React, { useCallback } from 'react';
import { FormikHelpers } from 'formik';

import { IEntityFieldContentProps } from '../internal/EntityFieldContent';
import EntityField from '../../../../../common/components/containers/EntityForm/internal/EntityField';
import FormCheckboxNormal from '../../../../../common/components/controls/FormCheckboxNormal';
import useT from '../../../utils/Translations/useT';
import useEntityFormContext from '../internal/useEntityFormContext';

export interface ICheckboxWithConfirmField
  extends IEntityFieldContentProps<boolean | unknown> {
  isDisabled?: boolean;
  name: string;
  label: string;
  required?: boolean;
  className?: string;
  popupText?: string;
  popupIcon?: string;
  popupButtons?: Array<string>;
  dangerMode?: boolean;
  condition?: (formikInstance?: FormikHelpers<any>) => boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onUncheck?: (formikInstance: FormikHelpers<any>) => boolean;
}

const CheckboxWithConfirmField: React.FC<ICheckboxWithConfirmField> = ({
  name,
  label,
  isDisabled = false,
  popupText,
  popupIcon = 'warning',
  popupButtons,
  dangerMode = true,
  condition = () => true,
  onUncheck: onExternalUncheck,
  ...rest
}) => {
  const t = useT();

  const formikInstance = useEntityFormContext();

  const handleOnChange = useCallback(
    ({ onChange }) => async data => {
      const {
        target: { checked },
      } = data;
      if (!checked && condition(formikInstance)) {
        const res = await swal({
          text: popupText || t('Are you sure ?'),
          icon: popupIcon,
          buttons: popupButtons || [t('Cancel'), t('Yes')],
          dangerMode,
        });
        if (res) {
          onChange(checked);
          onExternalUncheck && onExternalUncheck(formikInstance);
        }
      } else {
        onChange(checked);
      }
    },
    [
      t,
      popupText,
      popupIcon,
      popupButtons,
      dangerMode,
      condition,
      onExternalUncheck,
      formikInstance,
    ],
  );

  return (
    <EntityField name={name} {...rest}>
      {(isEditing, field) => (
        <FormCheckboxNormal
          label={label}
          onChange={handleOnChange(field.input)}
          {...field}
          isDisabled={isDisabled || !isEditing}
        />
      )}
    </EntityField>
  );
};

export default CheckboxWithConfirmField;
