import React, { useCallback, useMemo, useState } from 'react';

import { useTranslation } from '../../../../utils/Translations';
import Error from '../../../../utils/Error';
import useStaffPeople from '../../../../../data/hooks/useStaffPeople';
import MultiSelectBoxField from '../MultiSelectBoxField';
import TableCellTooltip from '../../../../../../common/components/utils/TableCellToolTip';

const MAX_VISIBLE_ITEMS = 2;

export default function PeopleMultiSelectField({ name }) {
  const { t } = useTranslation();
  const [isAddonTooltipOpen, setIsAddonTooltipOpen] = useState(false);

  const { loading, error, people } = useStaffPeople();

  const title = useMemo(
    () => (loading ? t('Loading Assignees...') : t('Assignee')),
    [t, loading],
  );

  const renderTooltipLine = useCallback(name => <p>{name}</p>, []);

  const handleAddonTooltipOpen = useCallback(() => {
    setIsAddonTooltipOpen(true);
  }, [setIsAddonTooltipOpen]);

  const handleAddonTooltipClose = useCallback(() => {
    setIsAddonTooltipOpen(false);
  }, [setIsAddonTooltipOpen]);

  const renderAddon = useCallback(
    (value: string) => {
      const splitted = value.split(',');
      // eslint-disable-next-line react/display-name, react/no-multi-comp
      return () => (
        <TableCellTooltip
          lines={splitted}
          minLineLength={1}
          trigger="hover"
          onClose={handleAddonTooltipClose}
          onOpen={handleAddonTooltipOpen}
        >
          {renderTooltipLine}
        </TableCellTooltip>
      );
    },
    [renderTooltipLine, handleAddonTooltipOpen, handleAddonTooltipClose],
  );

  const renderTitle = (value: string) => {
    const languages = value.split(',');
    if (!languages[0]) return t('None');
    const title = [languages[0]];

    if (languages.length > 1) {
      let i = 1;
      while (languages[i] && i < MAX_VISIBLE_ITEMS) {
        title.push(languages[i]);
        i++;
      }
    }

    if (languages.length > MAX_VISIBLE_ITEMS) {
      title.push('...');
    }
    return title.join(', ');
  };

  const renderTooltip = useCallback(
    (value: string) => {
      const languages = value.split(',');
      // eslint-disable-next-line react/display-name, react/no-multi-comp
      return (
        <TableCellTooltip lines={languages} minLineLength={1} trigger="hover">
          {renderTooltipLine}
        </TableCellTooltip>
      );
    },
    [renderTooltipLine],
  );

  if (error) {
    return <Error error={error} />;
  }

  return (
    <MultiSelectBoxField
      isLiveSearch
      noLabel
      disabled={loading}
      disableTitleTooltip={isAddonTooltipOpen}
      isRequired={false}
      itemTitlePropName="fullName"
      itemValuePropName="id"
      label={title}
      name={name}
      options={people}
      renderAddon={renderAddon}
      renderTitle={renderTitle}
      renderTooltip={renderTooltip}
    />
  );
}
