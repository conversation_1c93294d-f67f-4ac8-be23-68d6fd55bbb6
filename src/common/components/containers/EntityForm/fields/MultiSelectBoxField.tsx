import { find, flatten, isArray, join, map, isEmpty } from 'lodash';
import React, { useCallback } from 'react';
import classnames from 'classnames';
import {
  ITEM_TITLE_PROP_NAME,
  ITEM_VALUE_PROP_NAME,
} from '../../../controls/base/Multiselect';
import FormMultiselect from '../../../controls/FormMultiselect';
import ValueView from '../../../controls/ValueViews/ValueView';

import { useTranslation } from '../../../utils/Translations';

import EntityField from '../internal/EntityField';
import styles from './MultiSelectBoxField.scss';
import { DEFAULT_WRAPPER_CLASS_NAMES } from '../internal/EntityFieldContent';

export interface IMultiSelectBoxField {
  name: string;
  label: string;
  isRequired?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  value?: Array<any>;
  onChange?: Function;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: Array<any>;
  groupTitleKeys?: Array<string>;
  validate?: Function;
  itemTitlePropName?: string;
  itemValuePropName?: string;
  itemRenderer?: Function;
  className?: string;
  columns?: number;
  required?: boolean;
  disabled?: boolean;
  loading?: boolean;
  isLiveSearch?: boolean;
  orderOptionsBy?: string | string[];
  defaultValue?: Array<any>;
  hasSelectAll?: boolean;
  noLabel?: boolean;
  renderAddon?: (value: string) => () => JSX.Element;
  renderTitle?: (value: string) => string;
  renderTooltip?: (value: string) => JSX.Element;
  wrapperClassNames?: Partial<typeof DEFAULT_WRAPPER_CLASS_NAMES>;
  disableTitleTooltip?: boolean;
}
const MultiSelectBoxField: React.FC<IMultiSelectBoxField> = ({
  itemTitlePropName = ITEM_TITLE_PROP_NAME,
  itemValuePropName = ITEM_VALUE_PROP_NAME,
  options = [],
  groupTitleKeys,
  isRequired = false,
  validate = null,
  itemRenderer,
  className = '',
  name,
  label,
  noLabel,
  renderAddon,
  renderTitle,
  renderTooltip,
  disableTitleTooltip = false,
  ...rest
}) => {
  const { t } = useTranslation();

  const concatValue = useCallback(
    ({ input: { value } }) => {
      const flatOptions = flatten(options);

      return isArray(value)
        ? join(
            map(value, item => {
              const founded = find(flatOptions, { [itemValuePropName]: item });

              return founded ? founded[itemTitlePropName] : '';
            }),
            ', ',
          )
        : value;
    },
    [itemTitlePropName, itemValuePropName, options],
  );

  const validator = useCallback(
    fields => {
      if (isRequired && (!fields || fields.length === 0)) {
        return 'Required';
      }

      if (validate) {
        const error = validate(name);
        if (error) {
          return error;
        }
      }
    },
    [isRequired, name, validate],
  );

  return (
    <EntityField
      {...rest}
      name={name}
      required={isRequired}
      validate={validator}
    >
      {(editing, field, { required, disabled }) => {
        const value = concatValue(field);
        if (editing) {
          if (!renderTooltip) {
            return (
              <FormMultiselect
                className={className}
                disabled={disabled || field?.meta?.submitting}
                groupTitleKeys={groupTitleKeys}
                itemRenderer={itemRenderer}
                itemTitlePropName={itemTitlePropName}
                itemValuePropName={itemValuePropName}
                noLabel={noLabel}
                options={options}
                placeholder={label}
                required={required}
                {...field}
              />
            );
          }
          return (
            <div className="input-group w-100">
              <FormMultiselect
                className={className}
                disabled={disabled || field?.meta?.submitting}
                groupTitleKeys={groupTitleKeys}
                itemRenderer={itemRenderer}
                itemTitlePropName={itemTitlePropName}
                itemValuePropName={itemValuePropName}
                noLabel={noLabel}
                options={options}
                placeholder={label}
                required={required}
                {...field}
              />
              {renderTooltip && (
                <span
                  className={classnames(
                    styles.tooltipContainer,
                    'input-group-addon no-padding ',
                  )}
                >
                  {renderTooltip(value)}
                </span>
              )}
            </div>
          );
        }

        return (
          <ValueView
            disableTitleTooltip={disableTitleTooltip}
            label={label}
            noLabel={noLabel}
            renderAddon={renderAddon ? renderAddon(value) : undefined}
            title={value}
            value={
              // eslint-disable-next-line no-nested-ternary
              isEmpty(value)
                ? t('None')
                : renderTitle
                ? renderTitle(value)
                : value
            }
          />
        );
      }}
    </EntityField>
  );
};

export default MultiSelectBoxField;
