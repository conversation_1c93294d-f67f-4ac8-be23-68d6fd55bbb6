import { DocumentNode } from 'graphql';
import { find, forEach, get, isEmpty } from 'lodash';
import React, {
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useApolloClient, useQuery } from 'react-apollo';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import { WatchQueryFetchPolicy } from 'apollo-client';

import Statuses from '../../../../model/Statuses';
import { ITreeAdapter, ITreePlugins } from '../../../abstract/Trees';
import { handleFormErrors } from '../../../errors';
import getGqlOperationName from '../../../utils/getGqlOperationName';
import nodeSortingOptionsSequence from '../../../utils/nodeSortingOptionsSequence';
import Error from '../../utils/Error';
import LoadingMask from '../../utils/LoadingMask';
import useT from '../../utils/Translations/useT';
import { ITreeLayout } from '../BasicTrees/TreeLayoutConfig';
import { isRootNode, SelectionMode } from '../DynamicTree';
import ActionsAddon from '../NextTree/addons/ActionsAddon';
import { IActionsAddonAction } from '../NextTree/addons/ActionsAddon/ActionsAddon';
import StatusAddon from '../NextTree/addons/StatusAddon';
import StaticTree from '../StaticTree';
import GqlFullCrudTreeForm from './GqlFullCrudTreeForm';
import { setNodeExpanded } from '../NextTree/store/actions';
import useNextTreeStore from '../NextTree/store/useNextTreeStore';
import Expansion from '../NextTree/plugins/Expansion';
import {
  IStatusesModel,
  TStatusItem,
} from '../../containers/EntityForm/fields/TreeSelectorField/TreeSelectorField';
import Notifications from '../../../utils/Notifications';

export interface IGqlFullCrudTreeTree<T> extends ITreeLayout {
  key: string;
  title?: string;
  rootName: string;
  gql: {
    query: DocumentNode;
    variables?: object;
    skip?: boolean;
  };
  plugins?: ITreePlugins<T>;
  adapter?: ITreeAdapter<T>;
  filter?: {
    value?: object;
    component?: Function;
    onChange?: Function;
    hasDraftStatus?: boolean;
    props?: {
      [key: string]: number | string | undefined;
    };
    predicatesMeta?: {
      valuePropName: string;
      predicate: Function;
      includeChildren: boolean;
      collectVisibleIds: boolean;
    }[];
  };
  hasTitle?: boolean;
  hasStatusAddon?: boolean;
  hasExpandParentAfterSubmit?: boolean;
  statusFieldKey?: string;
  customStatuses?: IStatusesModel['BasicByValue'];
  customActiveStatus?: TStatusItem;
  customDeletedStatus?: TStatusItem;
  onlyRootModelsAllowed?: boolean;
}

export interface IGqlFullCrudTreeGql {
  single?: DocumentNode;
  singleVariables?: object;
  getParentGql?: (node: object) => DocumentNode;
  create?: DocumentNode;
  update?: DocumentNode;
  delete?: DocumentNode;
  fetchPolicy?: WatchQueryFetchPolicy;
}

type TGqlFullCrudTreeNode<T extends {}> = T & IGqlFullCrudTreeNode;

export interface IGqlFullCrudTreeNode {
  title?: string;
  gql?: IGqlFullCrudTreeGql;
  isThisNodeType: (node) => boolean;
  canCreateUnder?: (node, meta, staticModels) => boolean;
  isNodeDeleted?: (node) => boolean;
  isNodeDeletable?: (node) => boolean;
  renderNodeAddons?: (
    node: object,
    meta: object,
    selected: { setSelectedNode: object | null },
  ) => JSX.Element[];
  renderNodeActions?: (
    node: object,
    meta: object,
    selected: { setSelectedNode: object | null },
  ) => IActionsAddonAction[];
  component?: React.ElementType;
  onSelect?: (node, meta) => void;
  resolveTitle?: (node) => string;
}

export interface IGqlFullCrudTreeNodes {
  [key: string]: TGqlFullCrudTreeNode<Record<string, unknown>>;
}

export interface IGqlFullCrudTree<T> {
  tree: IGqlFullCrudTreeTree<T>;
  nodes: IGqlFullCrudTreeNodes;
}

function GqlFullCrudTree<T>(
  props: Readonly<PropsWithChildren<IGqlFullCrudTree<T>>>,
) {
  const { tree, nodes } = props;

  const {
    title,
    rootName,
    gql,
    plugins = {},
    adapter = {},
    filter,
    hasTitle,
    onlyRootModelsAllowed,
    hasStatusAddon = true,
    statusFieldKey = 'status',
    customStatuses,
    customActiveStatus,
    customDeletedStatus,
    hasExpandParentAfterSubmit = true,
    ...restTree
  } = tree;

  const t = useT();
  const { url } = useRouteMatch();
  const { push } = useHistory();
  const { dispatch } = useNextTreeStore();

  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedNodeMeta, setSelectedNodeMeta] = useState(null);
  const [loading, setLoading] = useState(false);
  const [hasQueryStartedLoading, setHasQueryStartedLoading] = useState(false);

  const [lastFilter, setLastFilter] = useState({
    status: Statuses.Active.value,
    searchQuery: '',
    ...filter?.value,
  });

  const queryRes = useQuery(gql.query, {
    skip: gql.skip,
    variables: gql.variables,
  });

  useEffect(() => {
    if (queryRes.loading) {
      setHasQueryStartedLoading(true);
    }
  }, [queryRes.loading]);

  // Update lastFilter when filter.value changes
  useEffect(() => {
    if (filter?.value) {
      setLastFilter(prevFilter => ({
        ...prevFilter,
        ...filter.value,
      }));
    }
  }, [filter?.value]);

  const { mutate } = useApolloClient();

  const refetchQueries = useMemo(
    () => [{ query: gql.query, variables: gql.variables }],
    [gql.query, gql.variables],
  );

  const onCancel = useCallback(() => {
    push(url);
    setSelectedNode(null);
  }, [push, url]);

  const onSelectionChange = useCallback(
    (node, meta) => {
      if (node !== selectedNode) {
        setSelectedNode(node);
        setSelectedNodeMeta(meta);

        if (!node) {
          push(url);
        } else {
          find(nodes, ({ isThisNodeType, onSelect }, key) => {
            if (isThisNodeType(node)) {
              onSelect && onSelect(node, meta);
              push(`${url}/${node.id}/edit/${key}`);
              return true;
            }
            return false;
          });
        }
      }
    },
    [nodes, push, selectedNode, url],
  );

  const formProps = useMemo(
    () => ({
      onCancel,
      refetchQueries,
      selectedNode,
      lastFilter,
      filter: filter?.value,
      setSelectedNode,
      onSelectionChange,
    }),
    [
      lastFilter,
      onCancel,
      onSelectionChange,
      refetchQueries,
      selectedNode,
      filter,
    ],
  );

  const nodeExpansionPlugins = useMemo(
    () => ({
      expansion: new Expansion(),
    }),
    [],
  );

  const _setNodeExpanded = useCallback(
    nodeId => {
      if (!hasExpandParentAfterSubmit) {
        return;
      }
      const { resolveNodeParentId } = adapter;
      const parentId = resolveNodeParentId && resolveNodeParentId(nodeId);
      parentId &&
        dispatch(
          setNodeExpanded(tree.key, parentId, true, {
            adapter,
            plugins: nodeExpansionPlugins,
          }),
          dummyAction,
        );
    },
    [
      nodeExpansionPlugins,
      hasExpandParentAfterSubmit,
      adapter,
      dispatch,
      tree.key,
    ],
  );

  const addItemComponent = useCallback(
    ({ match: { params } }) => (
      <GqlFullCrudTreeForm
        {...(nodes[params.key] as IGqlFullCrudTreeNode)}
        {...formProps}
        isNew
        nodeId={Number(params.id)}
        setNodeExpanded={_setNodeExpanded}
      />
    ),
    [formProps, nodes, _setNodeExpanded],
  );

  const editItemComponent = useCallback(
    ({ match: { params } }) => (
      <GqlFullCrudTreeForm
        {...nodes[params.key]}
        {...formProps}
        nodeId={Number(params.id)}
        selectedNodeMeta={selectedNodeMeta}
        setNodeExpanded={_setNodeExpanded}
      />
    ),
    [selectedNodeMeta, formProps, nodes, _setNodeExpanded],
  );

  const resetSelection = useCallback(() => {
    if (selectedNode !== null) {
      setSelectedNode(null);
    }

    return null;
  }, [selectedNode]);

  const handleSetLastFilter = useCallback(
    values => {
      setLastFilter(values);
      setSelectedNode(null);
      if (hasQueryStartedLoading && !queryRes.loading) {
        push(url);
      }
      filter?.onChange && filter.onChange(values);
    },
    [
      url,
      push,
      setLastFilter,
      setSelectedNode,
      filter,
      hasQueryStartedLoading,
      queryRes.loading,
    ],
  );

  const editNodeComponent = useMemo(
    () => (
      <Switch>
        <Route path={`${url}/:id/add/:key`} render={addItemComponent} />
        <Route path={`${url}/:id/edit/:key`} render={editItemComponent} />
        <Route path={`${url}/add/:key`} render={addItemComponent} />
        <Route exact path={url} render={resetSelection} />
      </Switch>
    ),
    [resetSelection, addItemComponent, editItemComponent, url],
  );

  const bindDeleteItem = useCallback(
    ({ gql, isThisNodeType }) => async node => {
      if (isThisNodeType(node)) {
        try {
          setLoading(true);
          const response = await handleFormErrors(
            mutate({
              mutation: gql.delete,
              variables: { id: node.id },
              refetchQueries,
              awaitRefetchQueries: true,
            }),
            t,
          );
          Notifications.success(t('Updated Successfully'), '', t);
          return response;
        } finally {
          setLoading(false);
        }
      }
    },
    [mutate, refetchQueries, t],
  );

  const bindAddItem = useCallback(
    key => node => {
      setSelectedNode(node);
      push(
        isRootNode(node) ? `${url}/add/${key}` : `${url}/${node.id}/add/${key}`,
      );
    },
    [push, url],
  );

  const staticModels = useMemo(
    () => get(queryRes, `data.${getGqlOperationName(gql.query)}`, []),
    [queryRes, gql.query],
  );

  const renderNodeAddons = useCallback(
    (node, meta) => {
      const { id } = node;
      const {
        isEditSessionActive,
        nodesMeta: { hasChild, hasActiveChild },
      } = meta;

      const addons: JSX.Element[] = [];
      const actions: IActionsAddonAction[] = [];

      if (!isRootNode(node) && hasStatusAddon) {
        addons.push(
          <StatusAddon
            key={`status_${id}`}
            customActiveStatus={customActiveStatus}
            customDeletedStatus={customDeletedStatus}
            customStatuses={customStatuses}
            status={node[statusFieldKey]}
          />,
        );
      }

      forEach(nodes, ({ renderNodeAddons, isThisNodeType }) => {
        if (renderNodeAddons && isThisNodeType(node)) {
          addons.push(...renderNodeAddons(node, meta, { setSelectedNode }));
        }
      });

      forEach(nodes, ({ renderNodeActions, isThisNodeType }) => {
        if (renderNodeActions && isThisNodeType(node)) {
          actions.push(...renderNodeActions(node, meta, { setSelectedNode }));
        }
      });

      if (isEditSessionActive) {
        forEach(nodes, ({ canCreateUnder, title }, key) => {
          if (canCreateUnder && canCreateUnder(node, meta, staticModels)) {
            actions.push({
              id: `add_${key}_${id}`,
              title: t('Add #{name}', { name: title }),
              action: bindAddItem(key),
            });
          }
        });

        forEach(nodes, (options, key) => {
          const {
            isThisNodeType,
            gql,
            isNodeDeleted = Statuses.isDeleted,
            isNodeDeletable = nodeIsDeletable,
          } = options;

          if (
            gql?.delete &&
            (!hasChild || !hasActiveChild) &&
            isThisNodeType(node) &&
            !isNodeDeleted(node) &&
            isNodeDeletable(node)
          ) {
            actions.push({
              id: `delete_${key}_${id}`,
              title: t('Delete'),
              action: bindDeleteItem(options),
              className: 'text-danger',
            });
          }
        });
      }

      if (!isEmpty(actions)) {
        addons.push(
          <ActionsAddon
            key={`actions_${node.id}`}
            actions={actions}
            node={node}
          />,
        );
      }

      return addons.flat();
    },
    [
      nodes,
      bindAddItem,
      t,
      bindDeleteItem,
      staticModels,
      hasStatusAddon,
      customStatuses,
      customActiveStatus,
      customDeletedStatus,
      statusFieldKey,
    ],
  );

  const _adapter = useMemo(
    () => ({
      renderNodeAddons,
      ...adapter,
    }),
    [adapter, renderNodeAddons],
  );

  const _plugins = useMemo(
    () => ({
      addNodeBuildMeta: addNodeBuildMetaHasChild,
      nodeIsSelectable,
      nodeSortingOptions: nodeSortingOptionsSequence,
      ...plugins,
    }),
    [plugins],
  );

  const _error = useMemo(() => queryRes.error, [queryRes.error]);

  const _loading = useMemo(
    () => !hasQueryStartedLoading || queryRes.loading || loading,
    [queryRes.loading, loading, hasQueryStartedLoading],
  );

  if (_error) {
    return <Error error={_error} />;
  }

  return (
    <LoadingMask active={_loading}>
      <StaticTree
        {...restTree}
        hasSyntheticRootNode
        adapter={_adapter}
        editNodeComponent={editNodeComponent}
        filter={{
          ...filter,
          onChange: handleSetLastFilter,
        }}
        hasTitle={hasTitle}
        isLoading={_loading}
        nodeExpansionOptions={{
          ...(_plugins?.nodeExpansionOptions || {}),
        }}
        onlyRootModelsAllowed={onlyRootModelsAllowed}
        plugins={_plugins}
        selectionMode={SelectionMode.SINGLE}
        staticModels={staticModels}
        syntheticRootNodeName={rootName}
        title={title}
        tree={tree.key}
        value={selectedNode}
        onSelectionChange={onSelectionChange}
      />
    </LoadingMask>
  );
}

export default GqlFullCrudTree;

export const addNodeBuildMetaHasChild = (node, parentMeta) => {
  if (parentMeta) {
    parentMeta.hasChild = true;

    if (Statuses.isActive(node)) {
      parentMeta.hasActiveChild = true;
    }
  }

  return { hasChild: false, hasActiveChild: false };
};

const nodeIsSelectable = node => !isRootNode(node);

function nodeIsDeletable() {
  return true;
}

const dummyAction = () => ({});
