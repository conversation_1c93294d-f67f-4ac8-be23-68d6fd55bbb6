import { TStatus } from '../../../../propTypes';
import { IGraphQLType } from '../../../IGraphQLType';
import { IAttendanceCode } from '../../../AttendanceCodeSet';

import IProgramGroupSessions from './IProgramGroupSessions';
import ISubjectTimetable from './ISubjectTimetable';

export interface ISubject extends IGraphQLType {
  __typename: 'Subject';
  id: number;
  name: string;
  code: string;
  status: TStatus;
  parentId: number | null;
  programGroupId: number;
  orgGroupId: number;
  markBook: number;
  gradeScaleId: number;
  attendanceCodeSet: number;
  attendanceCodeSetId: number;
  submissions: number;
  colour: string;
  sessionHoursCredits: boolean;
  timetable: boolean;
  mobileAppIcon: string;
  icon: string;
  sequence: number;

  programGroupSessions?: IProgramGroupSessions[];
  timetables?: ISubjectTimetable[];

  attendanceCodes?: IAttendanceCode[];
}

export default ISubject;
