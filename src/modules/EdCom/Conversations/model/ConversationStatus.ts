import { keyBy } from 'lodash';
import TConversationStatus from '../abstract/TConversationStatus';

interface IConversationStatusItem {
  value: TConversationStatus;
  name: string;
}

export const ACTIVE: IConversationStatusItem = {
  value: 'ACTIVE',
  name: 'Active',
};
export const HIDDEN: IConversationStatusItem = {
  value: 'HIDDEN',
  name: 'Hidden',
};
export const READ_ONLY: IConversationStatusItem = {
  value: 'READ_ONLY',
  name: 'Read-only',
};
export const DELETED: IConversationStatusItem = {
  value: 'DELETED',
  name: 'Deleted',
};

const Basic: IConversationStatusItem[] = [ACTIVE, HIDDEN, READ_ONLY, DELETED];
const BasicByValue = keyBy(Basic, 'value');

export default {
  Basic,
  BasicByValue,
};
