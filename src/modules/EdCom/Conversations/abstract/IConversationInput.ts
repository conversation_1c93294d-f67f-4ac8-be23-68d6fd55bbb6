import TConversationStatus from './TConversationStatus';
import { IFileAttachmentInput } from '../../../../common/abstract/IFileAttachments';
import TConversationTypePartCount from './TConversationTypePartCount';

interface IConversationInput {
  topic: string;
  status: TConversationStatus;
  dateFrom?: Date;
  dateTo?: Date;
  organisationId?: number;
  personEntityAllocationId?: number;
  logoAttachment?: IFileAttachmentInput;
  participantsAdded?: string[];
  participantsRemoved?: string[];
  typePartCount: TConversationTypePartCount;
}

export default IConversationInput;
