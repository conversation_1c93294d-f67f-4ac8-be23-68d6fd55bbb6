import IRecipientMinimal from '../../../../common/abstract/EdCom/IRecipientMinimal';
import { IFileAttachment } from '../../../../common/abstract/IFileAttachments';
import IRecipientPerson from '../../../../common/abstract/Messages/IRecipientPerson';
import IConversationItem from './IConversationItem';
import TConversationStatus from './TConversationStatus';
import TConversationTypePartCount from './TConversationTypePartCount';

interface IConversation {
  id: number;
  organisationId: number;
  personEntityAllocationId: number;
  topic: string;
  status: TConversationStatus;
  conversationArea?: string;
  moduleIcon?: string;
  dateFrom?: Date;
  dateTo?: Date;
  ownerPersonId: string;
  typePartCount: TConversationTypePartCount;

  createdBy: number;
  createdAt: Date;

  logoAttachment: IFileAttachment | null;
  createdByPerson: IRecipientPerson;
  lastConversationItem: IConversationItem;
  participantsCount: number;
  unreadConversationItemCount: number;
  unreadConversationNotesCount: number;
  participantsAdded?: IRecipientMinimal[];
  oneToOnePerson?: IRecipientMinimal;
}

export default IConversation;
