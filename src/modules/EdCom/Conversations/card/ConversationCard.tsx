import React, { FC, useMemo } from 'react';
import { capitalize, head } from 'lodash';
import classNames from 'classnames';

import { IEdComListItem } from '../../EdComCrud/context/EdComList';
import useT from '../../../../common/components/utils/Translations/useT';
import toEdanaTimestamp1 from '../../../../common/utils/edanaTimestamp1';
import styles from '../../Messages/card/MessageCard.scss';
import ImageRounded from '../../../../common/components/utils/ImageRounded';
import Icon from '../../../../common/components/utils/Icon';
import UnreadMessagesCount from '../../../../common/components/other/UnreadMessagesCount';

import IConversation from '../abstract/IConversation';

const ConversationCard: FC<IEdComListItem<IConversation>> = ({ item }) => {
  const {
    topic,
    conversationArea,

    createdByPerson,
    lastConversationItem,
    participantsCount,
    unreadConversationItemCount,
    logoAttachment,
  } = item;

  const t = useT();

  const author = useMemo(
    () =>
      `${capitalize(head(createdByPerson.firstName))} ${capitalize(
        createdByPerson.lastName,
      )}`,
    [createdByPerson],
  );

  const lastMessageTime = useMemo<string | undefined>(
    () =>
      lastConversationItem?.createdAt
        ? toEdanaTimestamp1(t, lastConversationItem?.createdAt)
        : undefined,
    [lastConversationItem, t],
  );

  return (
    <div className={styles.wrapper}>
      <div className={classNames(styles.image)}>
        <ImageRounded
          fileName={logoAttachment?.fileName}
          size="card"
          src={logoAttachment?.url}
        />
      </div>

      <div className={styles.content}>
        <div className={styles.title}>{topic}</div>
        <div className={styles.subtitle}>{conversationArea}</div>

        <div className={styles.subtitle}>
          <span className="mr-10">{author}</span>
        </div>
        <div className={styles.subtitle}>
          <span>{participantsCount}</span>
          <Icon className={classNames('mr-10', styles.userIcon)} name="user" />

          {unreadConversationItemCount ? (
            <UnreadMessagesCount count={unreadConversationItemCount} />
          ) : null}
        </div>
      </div>
      {lastMessageTime ? (
        <div className={classNames(styles.time, styles.subtitle)}>
          {lastMessageTime}
        </div>
      ) : null}
    </div>
  );
};

export default ConversationCard;
