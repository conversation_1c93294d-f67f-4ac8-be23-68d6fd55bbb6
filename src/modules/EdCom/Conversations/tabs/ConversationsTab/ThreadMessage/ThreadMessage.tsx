import classNames from 'classnames';
import copy from 'copy-to-clipboard';
import React, {
  FC,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useMutation } from 'react-apollo';
import { InView } from 'react-intersection-observer';

import AttachmentList from '../../../../../../common/components/controls/Attachments/lists/AttachmentList';
import { IDropdownAction } from '../../../../../../common/components/controls/Dropdown';
import MessageReport from '../../../../../../common/components/controls/MessageReport/MessageReport';
import Icon from '../../../../../../common/components/utils/Icon';

import PersonAvatar from '../../../../../../common/components/utils/PersonAvatar';
import SpinnerError from '../../../../../../common/components/utils/SpinnerError';
import Tooltip from '../../../../../../common/components/utils/Tooltip';
import useT from '../../../../../../common/components/utils/Translations/useT';
import { dummyMutation } from '../../../../../../common/data/dummyMutation';
import useCurrentUser from '../../../../../../common/data/hooks/useCurrentUser';
import toEdanaTimestamp1 from '../../../../../../common/utils/edanaTimestamp1';
import toEdanaTimestamp4 from '../../../../../../common/utils/edanaTimestamp4';
import getGqlOperationName from '../../../../../../common/utils/getGqlOperationName';
import swal2 from '../../../../../../common/utils/swal2';
import useContextMenu from '../../../../../../common/utils/useContextMenu';
import useIsConversationOpen from '../../../hooks/useIsConversationOpen';

import IThreadMessage from '../abstract/IThreadMessage';
import ThreadMessagesProvider from '../context/ThreadMessagesProvider';
import useAnchor from '../hook/useAnchor';
import useThreadMessageAction from '../hook/useThreadMessageAction';
import useThreadMessageReaded from '../hook/useThreadMessageReaded';
import useThreadMessagesList from '../hook/useThreadMessagesList';
import useThreadMessagesQuery from '../hook/useThreadMessagesQuery';
import { LIKE, STAR, UNLIKE, UNSTAR } from '../model/ReactionTypes';
import QuoteMessage from '../QuoteMessage';
import ThreadMessagesList from '../ThreadMessagesList/ThreadMessagesList';
import style from './ThreadMessage.scss';
import ThreadMessageActionSection from './ThreadMessageActionSection';
import ThreadMessageSeparator from './ThreadMessageSeparator';
import useEntityFormContext from '../../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import stripHtml from '../../../../../../common/utils/stripHtml';

const ThreadMessage: FC<IThreadMessage> = ({
  message,
  isCore = false,
  className,
}) => {
  const {
    conversationId,
    person,
    content,
    createdAt,
    updatedAt,
    likeCount,
    isStarred,
    isLiked,
    parentId,
    threadSize,
    id,
    attachments,
    isReaded,
    quote,
    quotedItemContent,
    isLastReadItem,
    unreadCount,
  } = message;

  const {
    likeQuery,
    starQuery,
    deleteQuery,
    reportQuery,
  } = useThreadMessageAction();
  const { replaceItem, parentContext, deleteItem } = useThreadMessagesList();
  const { setReadedItem } = useThreadMessageReaded();
  const { resetForm, setFieldValue } = useEntityFormContext();
  const t = useT();
  const isConversationOpen = useIsConversationOpen();

  const [isActionVisible, setActionVisibility] = useState(false);
  const [isRepliesOpen, setIsRepliesOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);

  const { anchorId, anchorParentId } = useAnchor();

  const messageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (anchorId === id) {
      requestAnimationFrame(() => {
        messageRef?.current?.scrollIntoView();
      });
    }
  }, []);

  useEffect(() => {
    if (anchorParentId === id) {
      setIsRepliesOpen(true);
    }
  }, []);

  const onReportClose = useCallback(
    (e?) => {
      e?.stopPropagation && e.stopPropagation();
      e?.preventDefault && e.preventDefault();
      setIsReportOpen(false);
    },
    [setIsReportOpen],
  );

  const onReportOpen = useCallback(() => {
    setIsReportOpen(true);
  }, [setIsReportOpen]);

  const {
    me: { id: currentUserId } = {},
    loading: userLoading,
    error: userError,
  } = useCurrentUser();

  const canEdit = person.id === currentUserId;

  const toggleReplies = useCallback(() => {
    setIsRepliesOpen(isOpen => !isOpen);
  }, []);

  const [onLike, { error: likeError, loading: likeLoading }] = useMutation(
    likeQuery || dummyMutation,
  );
  const [onStar, { error: starError, loading: starLoading }] = useMutation(
    starQuery || dummyMutation,
  );
  const [
    onDelete,
    { error: deleteError, loading: deleteLoading },
  ] = useMutation(deleteQuery || dummyMutation);

  const [
    onReport,
    { error: reportError, loading: reportLoading },
  ] = useMutation(reportQuery || dummyMutation);

  const handleReport = useCallback(
    async entity => {
      await onReport({
        variables: {
          params: {
            conversationItemId: message.id,
            reason: entity.reason,
            description: entity.description,
          },
        },
      });
      onReportClose();
    },
    [onReport, message.id, onReportClose],
  );

  const handleEdit = useCallback(() => {
    resetForm({ values: message });
  }, [resetForm, message]);

  const handleQuote = useCallback(() => {
    setFieldValue('threadQuote', message);
  }, [setFieldValue, message]);

  const actions = useMemo(() => {
    if (!isConversationOpen) {
      return [];
    }
    const actions: IDropdownAction[] = [
      {
        label: t('Copy'),
        onClick: () => copy(stripHtml(content)),
      },
    ];

    if (person.id === currentUserId) {
      actions.push({
        label: t('Delete'),
        disabled: deleteLoading,
        onClick: async () => {
          const { isConfirmed } = await swal2({
            icon: 'warning',
            title: t('Are you sure you want to delete this message?'),
            confirmButtonText: t('Yes'),
            cancelButtonText: t('Cancel'),
            deleteMode: true,
          });
          if (isConfirmed) {
            await onDelete({ variables: { id } });
            const parentItem = parentId && parentContext?.getItem(parentId);
            if (parentItem) {
              parentContext?.replaceItem({
                ...parentItem,
                threadSize: parentItem.threadSize - 1,
              });
            }
            deleteItem(id);
          }
        },
      });
    }

    if (person.id !== currentUserId) {
      actions.push({
        hoverTitle: t('Report'),
        onClick: onReportOpen,
        isCLoseOnClick: false,
        disabled: reportLoading,
        item: (
          <MessageReport
            isOpen={isReportOpen}
            onClose={onReportClose}
            onSubmit={handleReport}
          >
            {t('Report')}
          </MessageReport>
        ),
      });
    }

    return actions;
  }, [
    content,
    id,
    deleteItem,
    currentUserId,
    onDelete,
    person.id,
    deleteLoading,
    t,
    parentContext,
    parentId,
    isReportOpen,
    onReportOpen,
    onReportClose,
    handleReport,
    reportLoading,
    isConversationOpen,
  ]);

  const _onLike = useCallback(async () => {
    const { data } = await onLike({
      variables: {
        params: {
          conversationItemId: message.id,
          reactionType: message.isLiked ? UNLIKE : LIKE,
        },
      },
    });

    replaceItem(data[getGqlOperationName(likeQuery)]);
  }, [onLike, likeQuery, replaceItem, message.id, message.isLiked]);

  const _onStar = useCallback(async () => {
    const { data } = await onStar({
      variables: {
        params: {
          conversationItemId: message.id,
          reactionType: message.isStarred ? UNSTAR : STAR,
        },
      },
    });
    replaceItem(data[getGqlOperationName(starQuery)]);
  }, [onStar, replaceItem, starQuery, message.id, message.isStarred]);

  const { query, variables } = useThreadMessagesQuery();

  const _variables = useMemo(() => ({ ...variables, parentId: id }), [
    id,
    variables,
  ]);

  const markup = useMemo(() => ({ __html: content }), [content]);

  const renderTooltip = useCallback(
    () => (
      <>
        <p>{toEdanaTimestamp4(createdAt)}</p>
        <p>
          {t('Edited: ')}
          {toEdanaTimestamp4(updatedAt)}
        </p>
      </>
    ),
    [createdAt, updatedAt, t],
  );

  const handleVisibility = useCallback<
    (inView: boolean, entry: IntersectionObserverEntry) => void
  >(
    inView => {
      if (inView && !isReaded) {
        setReadedItem(message);
      }
    },
    [setReadedItem, message, isReaded],
  );

  const error =
    likeError || starError || userError || deleteError || reportError;
  const handleMouseEnter = useCallback(() => {
    setActionVisibility(true);
  }, [setActionVisibility]);

  const handleMouseLeave = useCallback(() => {
    setActionVisibility(false);
  }, [setActionVisibility]);

  const quoteMessage = useMemo(
    () =>
      quote && quotedItemContent
        ? { ...quote, content: quotedItemContent }
        : undefined,
    [quotedItemContent, quote],
  );

  const { isOpen, menu, handleContextMenu } = useContextMenu({
    actions,
    isKeepOpen: isReportOpen,
  });

  return (
    <SpinnerError error={error} loading={userLoading}>
      <div
        ref={messageRef}
        className={classNames(style.container, {
          open: isOpen,
          [style.hasBottomSeparator]: isLastReadItem,
        })}
        onContextMenu={handleContextMenu}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <InView
          className={classNames(
            style.main,
            {
              [style.reply]: !isCore,
              [style.hasBottomActions]: isConversationOpen,
            },
            className,
          )}
          onChange={handleVisibility}
        >
          <div className={style.contentContainer}>
            <PersonAvatar person={person} size="xs" />
            <div>
              <b>{person.fullName}</b>
              <div className={style.content}>
                <QuoteMessage message={quoteMessage} />
                {/*eslint-disable-next-line react/no-danger*/}
                <div dangerouslySetInnerHTML={markup} />
                {attachments?.length > 0 && (
                  <AttachmentList
                    isDownloadable
                    attachments={attachments}
                    editable={false}
                  />
                )}
              </div>
              {isConversationOpen && (
                <p className={style.bottomActionContainer}>
                  {isActionVisible && (
                    <>
                      {isCore && (
                        <span
                          className={style.bottomAction}
                          onClick={toggleReplies}
                        >
                          {t('Reply')}
                        </span>
                      )}
                      <span
                        className={style.bottomAction}
                        onClick={handleQuote}
                      >
                        {t('Quote')}
                      </span>
                      {canEdit && (
                        <span
                          className={style.bottomAction}
                          onClick={handleEdit}
                        >
                          {t('Edit')}
                        </span>
                      )}
                    </>
                  )}
                </p>
              )}
              <div className={style.bottomActionContainer}>
                {isCore && threadSize > 0 && (
                  <p className={style.bottomAction} onClick={toggleReplies}>
                    {t('#{count} Replies', { count: threadSize })}
                  </p>
                )}
                {unreadCount !== 0 && (
                  <p className={style.newMessage}>{`${unreadCount} New`}</p>
                )}
              </div>
            </div>
          </div>

          <div className={style.rightPart}>
            <p className={style.tooltip}>
              {updatedAt !== createdAt && (
                <Tooltip position="responsive" render={renderTooltip}>
                  <Icon name="pencil" />
                </Tooltip>
              )}
              {toEdanaTimestamp1(t, createdAt)}
            </p>
            <ThreadMessageActionSection
              actions={actions}
              isKeepOpen={isReportOpen}
              isLiked={isLiked}
              isLikeDisabled={likeLoading || !isConversationOpen}
              isStarrDisabled={starLoading || !isConversationOpen}
              isStarred={isStarred}
              likeCount={likeCount}
              onLike={_onLike}
              onStar={_onStar}
            />
          </div>
        </InView>
        {menu}
      </div>
      {isLastReadItem && (
        <ThreadMessageSeparator>{t('Unread Messages')}</ThreadMessageSeparator>
      )}
      {isRepliesOpen && (
        <ThreadMessagesProvider>
          <ThreadMessagesList
            conversationId={conversationId}
            gqlConnectionQuery={query}
            gqlConnectionVariables={_variables}
            messageId={id}
          />
        </ThreadMessagesProvider>
      )}
    </SpinnerError>
  );
};

export default memo(ThreadMessage);
