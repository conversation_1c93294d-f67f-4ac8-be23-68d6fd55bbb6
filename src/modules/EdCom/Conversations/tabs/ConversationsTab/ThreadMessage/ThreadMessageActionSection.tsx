import React from 'react';
import classNames from 'classnames';
import { isEmpty } from 'lodash';

import IconButton from '../../../../../../common/components/controls/IconButton';
import Dropdown, {
  IDropdownAction,
} from '../../../../../../common/components/controls/Dropdown';
import style from './ThreadMessageActionSection.scss';

interface IThreadMessageActionSection<T> {
  likeCount: number;
  isLiked: boolean;
  isStarred: boolean;
  actions: IDropdownAction[];
  onLike?: React.MouseEventHandler;
  onStar?: React.MouseEventHandler;
  isLikeDisabled?: boolean;
  isStarrDisabled?: boolean;
  isKeepOpen?: boolean;
}

export default function ThreadMessageActionSection<T>({
  likeCount,
  isStarred,
  isLiked,
  actions,
  onLike,
  onStar,
  isLikeDisabled = false,
  isStarrDisabled = false,
  isKeepOpen = false,
}: IThreadMessageActionSection<T>) {
  const likeIcon = isLiked ? 'heart3' : 'heart4';
  const starIcon = isStarred ? 'star-full' : 'star-empty';

  return (
    <div className={style.main}>
      <span className={classNames(style.like, style.action)}>{likeCount}</span>

      <IconButton
        className={classNames(style.like, style.action)}
        hasBackground={false}
        iconName={likeIcon}
        isDisable={isLikeDisabled}
        onClick={onLike}
      />
      <IconButton
        className={classNames(style.star, style.action)}
        hasBackground={false}
        iconName={starIcon}
        isDisable={isStarrDisabled}
        onClick={onStar}
      />
      <div className={classNames(style.action)}>
        {!isEmpty(actions) && (
          <Dropdown
            actions={actions}
            isKeepOpen={isKeepOpen}
            position="right"
          />
        )}
      </div>
    </div>
  );
}
