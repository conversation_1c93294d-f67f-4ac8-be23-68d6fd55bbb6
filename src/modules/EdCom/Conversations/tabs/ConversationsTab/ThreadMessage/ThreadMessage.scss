.container {
  border: 1px solid #e1e1e1;
  border-top: none;

  &:first-child {
    border-top: 1px solid #e1e1e1;
  }
}

.main {
  display: flex;
  justify-content: space-between;
  min-height: 70px;
  padding: 20px;
}

.reply {
  padding-left: 50px;
}

.hasBottomSeparator {
  border-bottom: none;
}

.hasBottomActions {
  padding-bottom: 0;
}

.contentContainer {
  display: flex;
}

.content {
  display: flex;
  flex-direction: column;
  word-break: break-all;
}

.rightPart {
  min-width: 80px;
  justify-content: space-between;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bottomActionContainer {
  display: flex;
  height: 20px;
}

.bottomAction {
  cursor: pointer;
  color: #1e88e5;
  margin-right: 5px;
}

.newMessage {
  color: #1e88e5;
}

.tooltip {
  display: flex;
}
