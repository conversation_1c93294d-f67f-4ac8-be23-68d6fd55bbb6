import React from 'react';
import TextEditorField from '../../../../../common/components/containers/EntityForm/fields/TextEditorField';
import AttachmentField from '../../../../../common/components/containers/EntityForm/fields/AttachmentField';
import { ED_COM_CONVERSATION_ATTACHMENTS } from '../../../../../fsCategories';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import QuoteMessageField from './QuoteMessageField';

interface IThreadMessageFromBody {
  className?: string;
}

export default function ThreadMessageFromBody({
  className,
}: IThreadMessageFromBody) {
  return (
    <EntityFormFieldSet className={className}>
      <QuoteMessageField />
      <TextEditorField
        noLabel
        required
        columns={1}
        config={{ heightMin: 150, pastePlain: true }}
        name="content"
        rows={4}
      />
      <AttachmentField
        isDownloadable
        categoryKey={ED_COM_CONVERSATION_ATTACHMENTS}
        name="attachments"
      />
    </EntityFormFieldSet>
  );
}
