import React, { useMemo } from 'react';
import IconButton from '../../../../../common/components/controls/IconButton';
import useT from '../../../../../common/components/utils/Translations/useT';
import toEdanaTimestamp9 from '../../../../../common/utils/edanaTimestamp9';
import { IThreadMessageItemQuote } from './abstract/IThreadMessageItem';
import styles from './QuoteMessage.scss';

interface IQuoteMessage {
  message?: IThreadMessageItemQuote;
  onCancel?: () => void;
}

export default function QuoteMessage({ message, onCancel }: IQuoteMessage) {
  const markup = useMemo(
    () => ({ __html: `"${message && message.content}"` }),
    [message],
  );
  const t = useT();
  if (!message) {
    return <></>;
  }

  return (
    <div className={styles.container}>
      <div>
        {/*eslint-disable-next-line react/no-danger*/}
        <div dangerouslySetInnerHTML={markup} />
        <span>
          {`${message.person.fullName}, ${toEdanaTimestamp9(
            t,
            message.createdAt,
          )}`}
        </span>
        <br />
        <span>___</span>
      </div>
      {onCancel && (
        <IconButton
          isSmall
          className={styles.removeIcon}
          hasBackground={false}
          iconName="cross"
          onClick={onCancel}
        />
      )}
    </div>
  );
}
