import React from 'react';
import { map } from 'lodash';

import ThreadMessage from './ThreadMessage/ThreadMessage';

import IThreadMessageItem from './abstract/IThreadMessageItem';

interface IThreadsMessageList<T extends IThreadMessageItem> {
  items: T[];
}

function ThreadsMessageList<T extends IThreadMessageItem>({
  items,
}: IThreadsMessageList<T>) {
  return (
    <>
      {map(items, item => (
        <ThreadMessage key={item.id} isCore message={item} />
      ))}
    </>
  );
}

export default ThreadsMessageList;
