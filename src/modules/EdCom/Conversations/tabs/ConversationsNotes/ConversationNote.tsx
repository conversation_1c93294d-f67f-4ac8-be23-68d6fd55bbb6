import React, { FC, useCallback, useMemo } from 'react';
import { InView } from 'react-intersection-observer';
import classNames from 'classnames';
import { useMutation } from 'react-apollo';
import { isEmpty } from 'lodash';

import style from '../ConversationsTab/ThreadMessage/ThreadMessage.scss';
import PersonAvatar from '../../../../../common/components/utils/PersonAvatar';
import AttachmentList from '../../../../../common/components/controls/Attachments/lists/AttachmentList';
import Tooltip from '../../../../../common/components/utils/Tooltip';
import Icon from '../../../../../common/components/utils/Icon';
import toEdanaTimestamp1 from '../../../../../common/utils/edanaTimestamp1';
import IConversationNote from './abstract/IConversationNote';
import toEdanaTimestamp4 from '../../../../../common/utils/edanaTimestamp4';
import useT from '../../../../../common/components/utils/Translations/useT';
import useThreadMessageReaded from '../ConversationsTab/hook/useThreadMessageReaded';
import Dropdown, {
  IDropdownAction,
} from '../../../../../common/components/controls/Dropdown';
import useThreadMessageAction from '../ConversationsTab/hook/useThreadMessageAction';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import useThreadMessagesList from '../ConversationsTab/hook/useThreadMessagesList';
import { dummyMutation } from '../../../../../common/data/dummyMutation';
import useIsConversationOpen from '../../hooks/useIsConversationOpen';
import useEntityFormContext from '../../../../../common/components/containers/EntityForm/internal/useEntityFormContext';
import actionSectionStyle from '../ConversationsTab/ThreadMessage/ThreadMessageActionSection.scss';

interface IConversationNoteProps {
  message: IConversationNote;
  onVisibility?: (
    message: IConversationNote,
    inView: boolean,
    entry: IntersectionObserverEntry,
  ) => void;
}

const ConversationNote: FC<IConversationNoteProps> = ({ message }) => {
  const {
    person,
    content,
    createdAt,
    updatedAt,
    id,
    attachments,
    isReaded,
  } = message;
  const t = useT();

  const { deleteItem } = useThreadMessagesList();
  const isConversationOpen = useIsConversationOpen();

  const { resetForm } = useEntityFormContext();

  const { deleteQuery } = useThreadMessageAction();
  const { me: { id: currentUserId } = {} } = useCurrentUser();

  const [onDelete, { loading: deleteLoading }] = useMutation(
    deleteQuery || dummyMutation,
  );

  const markup = useMemo(() => ({ __html: content }), [content]);
  const { setReadedItem } = useThreadMessageReaded<IConversationNote>();

  const handleVisibility = useCallback<
    (inView: boolean, entry: IntersectionObserverEntry) => void
  >(
    inView => {
      if (inView && !isReaded) {
        setReadedItem(message);
      }
    },
    [setReadedItem, message, isReaded],
  );

  const handleEdit = useCallback(() => {
    resetForm({ values: message });
  }, [resetForm, message]);

  const renderTooltip = useCallback(
    () => (
      <>
        <p>{toEdanaTimestamp4(createdAt)}</p>
        <p>
          {t('Edited: ')}
          {toEdanaTimestamp4(updatedAt)}
        </p>
      </>
    ),
    [createdAt, updatedAt, t],
  );

  const actions = useMemo(() => {
    let actions: IDropdownAction[] | null = null;

    if (person.id === currentUserId) {
      actions = [
        {
          label: t('Edit'),
          onClick: handleEdit,
        },
        {
          label: t('Delete'),
          disabled: deleteLoading,
          onClick: async () => {
            await onDelete({ variables: { id } });
            deleteItem(id);
            resetForm({ values: null });
          },
        },
      ];
    }

    return actions;
  }, [
    id,
    person.id,
    t,
    currentUserId,
    deleteItem,
    deleteLoading,
    onDelete,
    handleEdit,
    isConversationOpen,
  ]);

  return (
    <InView className={classNames(style.main)} onChange={handleVisibility}>
      <div className={style.contentContainer}>
        <PersonAvatar person={person} size="xs" />
        <div>
          <b>{person.fullName}</b>
          <div className={style.content}>
            {/* eslint-disable-next-line react/no-danger */}
            <div dangerouslySetInnerHTML={markup} />
            {attachments?.length > 0 && (
              <AttachmentList
                isDownloadable
                attachments={attachments}
                editable={false}
              />
            )}
          </div>
        </div>
      </div>
      <div className={style.rightPart}>
        <p className={style.tooltip}>
          {updatedAt !== createdAt && (
            <Tooltip position="responsive" render={renderTooltip}>
              <Icon name="pencil" />
            </Tooltip>
          )}
          {toEdanaTimestamp1(t, createdAt)}
        </p>
        {!isEmpty(actions) && (
          <div className={actionSectionStyle.main}>
            <Dropdown actions={actions} position="right" />
          </div>
        )}
      </div>
    </InView>
  );
};

export default ConversationNote;
