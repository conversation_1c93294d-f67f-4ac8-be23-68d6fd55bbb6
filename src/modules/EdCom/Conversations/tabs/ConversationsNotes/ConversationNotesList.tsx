import React, { FC, useCallback, useMemo } from 'react';
import { map } from 'lodash';

import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import IConversationNote from './abstract/IConversationNote';
import ThreadMessageForm from '../ConversationsTab/ThreadMessageForm';
import { InfinityScrollConnection } from '../../../../../common/utils/infinityScroll';
import query from '../../data/conversationNotesConnection.graphql';
import useThreadMessagesList from '../ConversationsTab/hook/useThreadMessagesList';
import ConversationNote from './ConversationNote';

const ConversationNotesList: FC = () => {
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const variables = useMemo(() => ({ conversationId: selectedItem?.id }), [
    selectedItem,
  ]);

  const { items, setItems } = useThreadMessagesList<IConversationNote>();
  const handleNewDataLoaded = useCallback(
    _items => {
      setItems(items => [..._items, ...items]);
    },
    [setItems],
  );

  const renderItems = useCallback(
    items =>
      map(items, item => <ConversationNote key={item.id} message={item} />),
    [],
  );

  return (
    <ThreadMessageForm>
      <InfinityScrollConnection<IConversationNote>
        hasEmptyPlaceholder
        gqlConnectionQuery={query}
        gqlConnectionVariables={variables}
        height={800}
        items={items}
        mode="DESC"
        renderItems={renderItems}
        onNewDataLoaded={handleNewDataLoaded}
      />
    </ThreadMessageForm>
  );
};

export default ConversationNotesList;
