import React, { FC } from 'react';

import ConversationStatus from '../../model/ConversationStatus';
import SelectBoxField from '../../../../../common/components/containers/EntityForm/fields/SelectBoxField';
import useT from '../../../../../common/components/utils/Translations/useT';

interface IConversationStatusField {
  isDisabled?: boolean;
}

const ConversationStatusField: FC<IConversationStatusField> = ({
  isDisabled,
}) => {
  const t = useT();
  return (
    <SelectBoxField
      required
      disabled={isDisabled}
      label={t('Status')}
      name="status"
      options={ConversationStatus.Basic}
    />
  );
};
export default ConversationStatusField;
