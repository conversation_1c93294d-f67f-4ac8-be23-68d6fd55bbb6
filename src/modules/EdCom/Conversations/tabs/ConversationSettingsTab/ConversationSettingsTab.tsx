import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { map, pick, get, isEmpty } from 'lodash';
import { useQuery } from 'react-apollo';
import { useHistory } from 'react-router-dom';

import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import EntityForm from '../../../../../common/components/containers/EntityForm';

import useT from '../../../../../common/components/utils/Translations/useT';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import { IFileAttachmentTemp } from '../../../../../common/abstract/IFileAttachments';
import TConversationStatus from '../../abstract/TConversationStatus';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import { ACTIVE } from '../../model/ConversationStatus';
import useIsCurrUserOwner from '../../hooks/useIsCurrUserOwner';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';
import IConversationInput from '../../abstract/IConversationInput';
import SubmitSection from '../../../../../common/components/containers/EntityForm/internal/SubmitSection';
import { GROUP, ONE_TO_ONE } from '../../model/ConversationTypePartCount';
import ConversationSettingsTabBody from './ConversationSettingsTabBody';
import useDeleteOrLeftConversation from '../../hooks/useDeleteOrLeftConversation';
import TConversationTypePartCount from '../../abstract/TConversationTypePartCount';
import conversationOneToOneGql from '../../data/conversationOneToOne.graphql';
import getGqlOperationName from '../../../../../common/utils/getGqlOperationName';
import useEdComTabCoreUrls from '../../../EdComCrud/context/EdComTabCoreUrls';
import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';
import { COMBINED_ID } from '../../../common/EdComOrganisationSelectorField';
import Notifications from '../../../../../common/utils/Notifications';

const ConversationSettingsTab: FC = () => {
  const {
    selectedItem,
    setUserPrefferences,
  } = useEdComSelectedItem<IConversation>();
  const t = useT();
  const { onSubmit, onGoBack } = useEdComRightForm<IConversationInput>();
  const { listRootUrl } = useEdComTabCoreUrls();
  const { push } = useHistory();

  const { data, loading: otoLoading, error: otoError } = useQuery(
    conversationOneToOneGql,
    {
      variables: { personId: selectedItem?.oneToOnePerson?.id },
      skip:
        selectedItem?.typePartCount !== ONE_TO_ONE.value ||
        !selectedItem?.oneToOnePerson?.id,
    },
  );

  useEffect(() => {
    const oneToOneKey = getGqlOperationName(conversationOneToOneGql);
    const oneToOne = get(data, oneToOneKey);
    if (oneToOne) {
      push(`${listRootUrl}/${oneToOne.id}`);
    }
  }, [data, listRootUrl, push]);

  const loading = otoLoading;
  const error = otoError;

  const isCurrUSerOwner = useIsCurrUserOwner();

  const _entity = useMemo<IConversationSettingsTabEntity>(
    () => ({
      id: selectedItem?.id,
      topic: selectedItem?.topic || '',
      status: selectedItem?.status || ACTIVE.value,
      dateFrom: selectedItem?.dateFrom,
      dateTo: selectedItem?.dateTo,
      organisationId: selectedItem?.organisationId,
      conversationArea: selectedItem?.conversationArea || 'Ed-com Conversation',
      personEntityAllocationId: selectedItem?.personEntityAllocationId,
      typePartCount: selectedItem?.typePartCount || GROUP.value,
      logoAttachment: selectedItem?.logoAttachment as IFileAttachmentTemp,
      participantsAdded: selectedItem?.participantsAdded,
    }),
    [selectedItem],
  );

  const _onSubmit = useCallback<
    (values: IConversationSettingsTabEntity) => void
  >(
    async ({
      orgStructureLevelId,
      conversationArea,
      participantsAdded,
      participantsRemoved,
      personEntityAllocationId,
      organisationId,
      EDCOM_ID,
      ...entity
    }) => {
      const _participantsAdded = participantsAdded
        ?.filter(({ id }) => typeof id === 'string')
        .map(({ id }) => id);

      if (isEmpty(_participantsAdded)) {
        Notifications.error('Please add at lease one recipient', '', t);
        return false;
      }

      await setUserPrefferences({
        organisationId,
        personEntityAllocationId,
        orgStructureLevelId,
      });
      if (onSubmit) {
        return onSubmit({
          ...entity,
          logoAttachment:
            entity.logoAttachment &&
            pick(entity.logoAttachment, [
              'fileId',
              'uploadToken',
              'description',
              'fileName',
            ]),
          participantsAdded: _participantsAdded,
          participantsRemoved: map(participantsRemoved, 'id'),
          organisationId,
          personEntityAllocationId,
        });
      }
    },
    [onSubmit],
  );

  const { button: leftButton, loading: bl } = useDeleteOrLeftConversation();

  const button = useCallback(
    ({ isNew, isDirty: _isDirty, isSubmitting }) => (
      <SubmitSection
        isEditing
        createLabel="Create"
        goBackLabel="Go Back"
        hasClearFormButton={false}
        isDirty={_isDirty}
        isNew={isNew}
        isSubmitting={isSubmitting}
        leftBottomSection={leftButton}
        updateLabel="Update"
        onGoBack={onGoBack}
      />
    ),
    [onGoBack, leftButton],
  );

  return (
    <SpinnerError error={error} loading={loading || bl}>
      <EntityForm<IConversationSettingsTabEntity>
        hasLeavePrompt
        entity={_entity}
        isNew={!selectedItem?.id}
        isReadOnly={!isCurrUSerOwner}
        submitSectionRenderer={button}
        onCancel={onGoBack}
        onSubmit={_onSubmit}
      >
        <ConversationSettingsTabBody />
      </EntityForm>
    </SpinnerError>
  );
};

interface IConversationSettingsTabEntity extends IBasicEntity {
  topic: string;
  status: TConversationStatus;
  dateFrom?: Date;
  dateTo?: Date;
  organisationId?: number;
  personEntityAllocationId?: number;
  orgStructureLevelId?: number;
  logoAttachment?: IFileAttachmentTemp;
  conversationArea?: string;
  participantsAdded?: IRecipientMinimal[];
  participantsRemoved?: IRecipientMinimal[];
  typePartCount: TConversationTypePartCount;
  [COMBINED_ID]?: string;
}

export default ConversationSettingsTab;
