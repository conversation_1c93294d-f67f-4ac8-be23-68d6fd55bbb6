import React, { FC, useCallback, useMemo, useState } from 'react';
import useT from '../../../../../common/components/utils/Translations/useT';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import useEdComTempItem from '../../../EdComCrud/context/EdComTempItem';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';
import IMessageParticipantsInput from '../../../Messages/abstract/IMessageParticipantsInput';

import EntityForm from '../../../../../common/components/containers/EntityForm';
import { IBasicEntity } from '../../../../../common/components/containers/EntityForm/EntityForm';
import IConversation from '../../abstract/IConversation';
import IConversationTemp from '../../abstract/IConversationTemp';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import ConversationsParticipantsTabBody from './ConversationsParticipantsTabBody';
import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';

const ConversationsParticipantsTab: FC = () => {
  const t = useT();
  const { loading, me, error } = useCurrentUser();
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const { tempData, setTempData } = useEdComTempItem<IConversationTemp>();
  const { onGoNext, onCancel } = useEdComRightForm<IMessageParticipantsInput>();

  const defaultParticipants = useMemo(() => (me && [me]) || [], [me]);

  const _onSubmit = useCallback<
    (values: ConversationsParticipantsTabEntity) => void
  >(
    ({ participantsAdded, participantsRemoved }) => {
      setTempData({
        participantsAdded: selectedItem?.id
          ? participantsAdded
          : [...participantsAdded, ...defaultParticipants],
        participantsRemoved,
      });
      onGoNext();
    },
    [setTempData, defaultParticipants, onGoNext, selectedItem],
  );

  const [_participants, _setParticipants] = useState<IRecipientMinimal[]>(
    tempData.participants || defaultParticipants,
  );

  const _entity = useMemo<ConversationsParticipantsTabEntity>(
    () => ({
      participants: _participants,
      participantsRemoved: [],
      participantsAdded: [],
      id: selectedItem?.id,
    }),
    [_participants, selectedItem],
  );

  return (
    <SpinnerError error={error} loading={loading}>
      <EntityForm<ConversationsParticipantsTabEntity>
        hasLeavePrompt
        isNew
        createLabel={t('Next')}
        entity={_entity}
        hasCreateMessage={!!selectedItem?.id}
        successMessageText={t('Updated successfully')}
        onCancel={onCancel}
        onSubmit={_onSubmit}
      >
        <ConversationsParticipantsTabBody setParticipants={_setParticipants} />
      </EntityForm>
    </SpinnerError>
  );
};

export default ConversationsParticipantsTab;

interface ConversationsParticipantsTabEntity extends IBasicEntity {
  participants: IRecipientMinimal[];
  participantsAdded: IRecipientMinimal[];
  participantsRemoved: IRecipientMinimal[];
}
