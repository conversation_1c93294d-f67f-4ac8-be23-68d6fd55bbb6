import React, { FC, useCallback } from 'react';
import { useMutation } from 'react-apollo';

import IRecipientMinimal from '../../../../../common/abstract/EdCom/IRecipientMinimal';
import EntityFormFieldSet from '../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import { IDropdownAction } from '../../../../../common/components/controls/Dropdown';
import { TPerson } from '../../../../../common/components/utils/PersonAvatar';
import SpinnerError from '../../../../../common/components/utils/SpinnerError';
import useT from '../../../../../common/components/utils/Translations/useT';
import useCurrentUser from '../../../../../common/data/hooks/useCurrentUser';
import { handleFormErrors } from '../../../../../common/errors';
import swal2 from '../../../../../common/utils/swal2';
import useRecipientsPair from '../../../common/useRecipientsPair';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import IConversation from '../../abstract/IConversation';
import changeConversationOwnerGql from '../../data/changeConversationOwner.graphql';
import conversationParticipantsQuery from '../../data/conversationParticipants.graphql';
import conversationParticipantsCountQuery from '../../data/conversationParticipantsCount.graphql';
import useIsConversationOpen from '../../hooks/useIsConversationOpen';
import useIsCurrUserOwner from '../../hooks/useIsCurrUserOwner';
import isOwner from '../../utils/isOwner';

const ConversationsParticipantsTabBody: FC<IConversationsParticipantsTabBodyProps> = ({
  setParticipants,
}) => {
  const t = useT();
  const { selectedItem } = useEdComSelectedItem<IConversation>();
  const { me, loading, error } = useCurrentUser();
  const isCurrUserOwner = useIsCurrUserOwner();

  const [changeOwnerMutation] = useMutation(changeConversationOwnerGql);
  const isConversationOpen = useIsConversationOpen();

  const resolvePersonName = useCallback(
    (person: TPerson) => {
      const ownerLabel = isOwner(me, person, selectedItem)
        ? ` (${t('Owner')})`
        : '';
      return `${person.fullName}${ownerLabel}`;
    },
    [selectedItem, me, t],
  );

  const canBeDeleted = useCallback(
    (person: TPerson) =>
      (isCurrUserOwner && !isOwner(me, person, selectedItem)) ||
      (!isOwner(me, person, selectedItem) && person.id === me.id),
    [selectedItem, isCurrUserOwner, me],
  );

  const handleChangeOwner = useCallback(
    (item: TPerson) => async () => {
      const { isConfirmed } = await swal2<1 | 0>({
        icon: 'warning',
        title: t(
          'You may no longer be able to change settings, and the new owner may also remove your access.',
        ),
        confirmButtonText: t('Yes'),
        cancelButtonText: t('Cancel'),
        deleteMode: true,
      });

      if (!isConfirmed) {
        return false;
      }

      const result = await handleFormErrors(
        changeOwnerMutation({
          variables: {
            params: {
              conversationId: selectedItem?.id,
              ownerPersonId: item.id,
            },
          },
        }),
        t,
      );

      if (selectedItem) {
        selectedItem.ownerPersonId = item.id as string;
      }

      return result;
    },
    [changeOwnerMutation, selectedItem, t],
  );

  const resolveCardActions = useCallback(
    (person: TPerson) => {
      const actions: IDropdownAction[] = [];
      if (
        selectedItem?.id &&
        !isOwner(me, person, selectedItem) &&
        isCurrUserOwner
      ) {
        actions.push({
          label: t('Make Owner'),
          onClick: handleChangeOwner(person),
        });
      }
      return actions;
    },
    [t, handleChangeOwner, isCurrUserOwner, me, selectedItem],
  );

  const resolveItemDisable = useCallback(
    (person: TPerson, isChecked) => isChecked && !canBeDeleted(person),
    [canBeDeleted],
  );

  const { table, field } = useRecipientsPair({
    listQuery: conversationParticipantsQuery,
    listCountQuery: conversationParticipantsCountQuery,
    queryListVariables: { conversationId: selectedItem?.id },
    gqlSearchParams: { conversationId: selectedItem?.id, withGroups: true },
    isSkip: !selectedItem?.id,

    fieldLabel: t('Search for Participants'),
    name: 'participants',
    fillRecipients: setParticipants,
    resolvePersonName,
    canBeDeleted,
    resolveCardActions,
    resolveItemDisable,
    isReadOnly: !isConversationOpen,
    orderItemsBy: ['lastName', 'firstName'],
  });

  return (
    <SpinnerError error={error} loading={loading}>
      {table}
    </SpinnerError>
  );
};

export default ConversationsParticipantsTabBody;

interface IConversationsParticipantsTabBodyProps {
  setParticipants: (participants: IRecipientMinimal[]) => void;
}
