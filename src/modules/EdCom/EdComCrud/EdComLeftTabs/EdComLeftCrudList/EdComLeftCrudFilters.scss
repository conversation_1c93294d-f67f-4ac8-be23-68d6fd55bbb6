.filtersFix {

  .item:first-child {
    border-top: 1px solid lightgray;
  }

  :global(.multiselect.btn-default) {
    border-bottom-width: 0;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  :global(.btn-group > button){
    height: 100%;
  }

  :global(.dropdown-toggle) {
    border-bottom-width: 0;
  }

  :global(.form-control) {
    border-bottom-width: 0;
  }

  :global(a) {
    color: inherit;
  }

  :global(table) {
    width: 100%;
  }
}

.item {
  min-height: 40px !important;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.statusFilterContainer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.searchBoxContainer {
  display: flex;
  flex-direction: row;
}

.searchField {
  width: 50%;
}

.searchFieldGrowth {
  flex-grow: 1;
  width: 50%;
}

.filterButton {
  flex-grow: 0;
  border-left: 1px solid lightgray;
  padding: 10px 0 8px 16px;
  color: gray !important;
}

.panelButtons {
  margin-right: -7px;
}

.searchMode {
  width: 100px;
  border-left: 1px solid lightgray;
  flex-grow: 1;
}

.searchModeButton  {
  height: 100%;
  padding: 0 5px 0 5px;

  button {
    height: 100%;
    padding: 0 !important;
    border: unset !important;
  }
}

.inputSpacing {
  padding-right: 74px !important
}

.inputSpacingConv {
  padding-right: 185px !important
}

@media (max-width: 1000px) {
  .inputSpacing {
    padding-right: 50px !important
  }
  .inputSpacingConv {
    padding-right: 50px !important
  }
}

@media (max-width: 400px) {
  .inputSpacing {
    padding-right: 90px !important
  }
  .inputSpacingConv {
    padding-right: 190px !important
  }
}