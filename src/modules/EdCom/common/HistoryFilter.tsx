import moment, { Moment } from 'moment';
import React, { FC } from 'react';

import SelectBox from '../../../common/components/controls/base/SelectBox';
import useT from '../../../common/components/utils/Translations/useT';
import useEnumerableModel from '../../../common/data/hooks/useEnumerableModel';

import {
  format,
  TransportDateTimeFormat,
} from '../../../common/utils/dateTime';
import { THistory } from '../abstract';
import { TFilterComponent } from '../EdComCrud/context/EdComFilterBar';
import History, { LAST_7_DAYS, TODAY } from '../model/History';

const HistoryFilter: FC<TFilterComponent<THistory>> = ({ value, onChange }) => {
  const t = useT();

  const { options } = useEnumerableModel(History);

  return (
    <SelectBox
      hasInlineLabel
      itemValuePropName="value"
      options={options}
      title={t('History')}
      value={value}
      onChange={onChange}
    />
  );
};

export default HistoryFilter;

export const getLastMessageFrom = (
  history: THistory,
  now: Moment,
): string | undefined => {
  if (history === LAST_7_DAYS.value) {
    return format(moment(now).subtract(1, 'week'), TransportDateTimeFormat);
  } else if (history === TODAY.value) {
    return format(moment(now).subtract(1, 'days'), TransportDateTimeFormat);
  }
  return undefined;
};
