import React, { useCallback, useMemo } from 'react';
import { useMutation } from 'react-apollo';
import { isEmpty } from 'lodash';
import IDeleteMessageItemInput from '../../../../../common/abstract/Messages/IDeleteMessageItemInput';
import IMessage from '../../../../../common/abstract/Messages/IMessage';
import RoundedLinkButton from '../../../../../common/components/controls/RoundedLinkButton';
import useInputPlaceholder from '../../../../../common/components/forms/MessageItems/hooks/useInputPlaceholder';
import useIsMessageMine from '../../../../../common/components/forms/MessageItems/hooks/useIsMessageMine';
import { ITranslationContext } from '../../../../../common/components/utils/Translations';
import useT from '../../../../../common/components/utils/Translations/useT';
import { handleFormErrors } from '../../../../../common/errors';
import swal2 from '../../../../../common/utils/swal2';
import MessageType, {
  isBroadcastOrChannel,
  isChat,
} from '../../../../../model/MessageType';
import { ACTIVE } from '../../../../PeopleRecords/PersonDatabase/PersonInfo/PersonInfoTabs/General/Contracts/model/ContractStatus';
import useEdComGql from '../../../EdComCrud/context/EdComGql';
import useEdComRightForm from '../../../EdComCrud/context/EdComRightForm';
import useEdComSelectedItem from '../../../EdComCrud/context/EdComSelectedItem';
import deleteMessageMutation from '../../data/deleteMessage.graphql';
import leaveMessageMutation from '../../data/leaveMessage.graphql';
import Notifications from '../../../../../common/utils/Notifications';

export default function useDeleteOrLeftChat(): IUseDeleteOrLeftChatRes {
  const t = useT();

  const { selectedItem } = useEdComSelectedItem<IMessage>();
  const { onCancel } = useEdComRightForm<IMessage>();
  const { refetchQueries } = useEdComGql();

  const isMessageMine = useIsMessageMine();

  const isDeleting = useMemo<boolean>(() => isMessageMine(selectedItem), [
    selectedItem,
    isMessageMine,
  ]);

  const [mutation, { loading }] = useMutation<
    { deleteMessage: IDeleteResponse },
    { id: number; params: IDeleteMessageItemInput } | { id: number }
  >(isDeleting ? deleteMessageMutation : leaveMessageMutation, {
    refetchQueries,
  });

  const inputPlaceholder = useInputPlaceholder(
    isDeleting && isChat(selectedItem),
  );

  const buttonLabel = useMemo<string>(
    () => t(isDeleting ? 'Delete' : 'Leave'),
    [isDeleting, t],
  );

  const swalTitle = useMemo<string>(
    () => getMessageDeleteMessage(t, selectedItem, isDeleting),
    [t, isDeleting, selectedItem],
  );

  const confirm = useCallback<
    () => Promise<{ isConfirmed: boolean; value?: boolean }>
  >(async () => {
    const { isConfirmed, value } = await swal2<1 | 0>({
      deleteMode: true,
      input: inputPlaceholder ? 'checkbox' : undefined,
      inputPlaceholder,
      title: swalTitle,
      confirmButtonText: buttonLabel,
      cancelButtonText: t('Cancel'),
      icon: 'warning',
    });

    return {
      isConfirmed,
      value: inputPlaceholder ? !!value : isBroadcastOrChannel(selectedItem),
    };
  }, [selectedItem, swalTitle, t, inputPlaceholder, buttonLabel]);

  const runAction = useCallback<(forEveryone?: boolean) => Promise<void>>(
    async forEveryone => {
      if (selectedItem) {
        const response = await handleFormErrors(
          mutation({
            variables: isDeleting
              ? { id: selectedItem.id, params: { forEveryone } }
              : { id: selectedItem.id },
          }),
          t,
        );
        if (response) Notifications.success(t('Deleted Successfully'), '', t);
        return response;
      }
    },
    [t, selectedItem, mutation, isDeleting],
  );

  const handleAction = useCallback(async () => {
    if (selectedItem?.id) {
      const { value, isConfirmed } = await confirm();
      const withParticipants =
        (selectedItem?.isOwner &&
          !isEmpty(selectedItem?.organisationAllocationNames)) ||
        value;

      if (isConfirmed) {
        await runAction(withParticipants);
        if (!isDeleting || value) {
          onCancel();
        }
      }
    }
  }, [onCancel, isDeleting, runAction, confirm, selectedItem]);

  const button = useMemo<JSX.Element | undefined>(() => {
    if (
      selectedItem?.id &&
      (!isDeleting ||
        (isChat(selectedItem) && selectedItem.status === ACTIVE.value) ||
        (isBroadcastOrChannel(selectedItem) &&
          selectedItem.status === ACTIVE.value))
    ) {
      return (
        <RoundedLinkButton
          additionClasses="text-danger"
          title={buttonLabel}
          onClick={handleAction}
        >
          {buttonLabel}
        </RoundedLinkButton>
      );
    }

    return undefined;
  }, [isDeleting, selectedItem, handleAction, buttonLabel]);

  return {
    button,
    loading,
  };
}

interface IUseDeleteOrLeftChatRes {
  button?: JSX.Element;
  loading: boolean;
}

export const getMessageDeleteMessage = (
  t: ITranslationContext['t'],
  selectedItem?: IMessage,
  isDeleting = true,
): string =>
  t(
    isDeleting
      ? 'Are you sure you want to delete this #{name}?'
      : 'Are you sure you want to leave this #{name}?',
    {
      name: selectedItem?.type
        ? t(MessageType.BasicByValue[selectedItem?.type].name)
        : undefined,
    },
  );
