import React, { useCallback, useContext, useMemo, useState } from 'react';
import { isEqual, map, pick } from 'lodash';
import { useParams, useLocation } from 'react-router-dom';
import classNames from 'classnames';

import { uniqueArray } from '../../../../../../common/formHelpers';
import EntityForm from '../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import CodeField from '../../../../../../common/components/containers/EntityForm/fields/CodeField';
import ColourField from '../../../../../../common/components/containers/EntityForm/fields/ColourField';
import EntityNameField from '../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusField from '../../../../../../common/components/containers/EntityForm/fields/StatusField';
import IconSelectorField from '../../../../../../common/components/containers/EntityForm/fields/IconSelectorField';
import ContentBox from '../../../../../../common/components/utils/ContentBox';
import Spinner from '../../../../../../common/components/utils/Spinner';
import Tabs from '../../../../../../common/components/utils/Tabs';
import useProgramGroupTaskTypes from '../../../../../../common/data/hooks/useProgramGroupTaskTypes';
import Statuses from '../../../../../../model/Statuses';
import ProgramGroupCatalogueSubjectLms from './ProgramGroupCatalogueSubjectLms';
import ProgramGroupCatalogueSubjectSchedule from './ProgramGroupCatalogueSubjectSchedule';
import IProgramSubjectFolder from '../../../../../../common/abstract/OrganisationGroup/ProgramGroups/Subjects/IProgramSubjectFolder';
import SequenceField from '../../../../../../common/components/containers/EntityForm/fields/SequenceField';

import useNextSubjectSequence from './hooks/useNextSubjectSequence';
import SubjectLevelTreeField from './fields/SubjectLevelTreeField';
import TreeContext from '../../../../../../common/components/dataViews/NextTree/TreeContext';
import { isRootNode } from '../../../../../../common/components/dataViews/DynamicTree/constants';
import {
  isFolder,
  TProgramStructureTree,
} from '../../../../../../model/ProgramGroupCatalogueTypeNames';
import useT from '../../../../../../common/components/utils/Translations/useT';
import ProgramGroupCatalogueSubjectSubmitSection from '../../../common/ProgramGroupCatalogueSubjectSubmitSection';
import FormTitle from '../../../../../../common/components/utils/FormTitle';
import ProgramGroupCatalogueSubjectResources from './ProgramGroupCatalogueSubjectResources';
import styles from './ProgramGroupCatalogueSubjectLms.scss';
import AttachmentsWithCaptionField from '../../../../../../common/components/containers/EntityForm/fields/AttachmentsWithCaptionField';

const MAX_CODE_LENGTH = 15;
const SUBJECT_ATTACHMENTS = 'subject-attachments';
const MAX_DESCRIPTION_LENGTH = 100;

export interface ProgramGroupCatalogueSubjectFormEntity
  extends IProgramSubjectFolder {
  _tempId?: string;
}

export interface IProgramGroupCatalogueSubjectForm {
  onCancel: () => void;
  selectedNode: ProgramGroupCatalogueSubjectFormEntity;
  onSubmit: (
    value: ProgramGroupCatalogueSubjectFormEntity,
    options: object,
  ) => Promise<void>;
  setSelectedNode: (value: React.SetStateAction<null>) => void;
  programActivityName: string;
  programGroupId: number;
  entityName: string;
}

const ProgramGroupCatalogueSubjectForm: React.FC<IProgramGroupCatalogueSubjectForm> = ({
  entityName,
  onSubmit,
  selectedNode,
  onCancel,
  programActivityName,
  setSelectedNode,
}) => {
  const location = useLocation();
  const { groupTypeId } = useParams();
  const { parentId, programGroupId, orgGroupId } = selectedNode;
  const {
    treeState: { inputModels },
  } = useContext(TreeContext);
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);

  const parentLevel = useMemo(() => {
    if (!selectedNode.parentId) {
      return inputModels.find(isRootNode);
    }

    return inputModels.find(
      (model: TProgramStructureTree) =>
        model.id === selectedNode.parentId && isFolder(model),
    );
  }, [selectedNode, inputModels]);

  const { loading, programGroupTaskTypes } = useProgramGroupTaskTypes(
    Number(groupTypeId),
  );
  const {
    nextSequence,
    loading: subjectSequenceLoading,
    error: subjectSequenceError,
  } = useNextSubjectSequence({
    parentId,
    programGroupId,
    orgGroupId,
  });

  const _handleSubmit = useCallback(
    ({ parentLevel, gptVersion, subjectAttachments, ...entity }, options) =>
      onSubmit(
        {
          ...entity,
          subjectAttachments: map(subjectAttachments, a =>
            pick(a, ['fileId', 'uploadToken', 'description', 'fileName']),
          ),
          parentId: isRootNode(parentLevel) ? null : parentLevel.id,
          attendanceCodeSetId: entity.attendanceCodeSet
            ? entity.attendanceCodeSetId
            : null,
        },
        options,
      ),
    [onSubmit],
  );

  const t = useT();
  const { id } = selectedNode;
  const uid = `catalogue-tree-${id || selectedNode._tempId}`;

  const title = !selectedNode.id
    ? t('Add #{programActivityName}', { programActivityName })
    : selectedNode.name;

  const entity = useMemo(
    () => ({
      learningPlan: programGroupTaskTypes.map(type => type.id),
      ...selectedNode,
      parentLevel,
      sequence: selectedNode.sequence || nextSequence,
    }),
    [parentLevel, programGroupTaskTypes, nextSequence, selectedNode],
  );
  const handleValidate = useCallback(
    values => {
      const errors = {};

      uniqueArray(values, 'timetables', 'periodType', errors, t);

      return errors;
    },
    [t],
  );

  const saveData = useCallback(
    values => {
      if (!isEqual(entity, values)) {
        setSelectedNode(values);
        setIsSubmitDisabled(false);
      }
    },
    [entity, setSelectedNode],
  );

  const submitSectionRenderer = useCallback(
    ({ isSubmitting, isEditing, isDirty }) => (
      <ProgramGroupCatalogueSubjectSubmitSection<ProgramGroupCatalogueSubjectFormEntity>
        isNew={!id}
        isSubmitDisabled={!isSubmitDisabled ? false : !isEditing || !isDirty}
        isSubmitting={isSubmitting}
        onCancel={onCancel}
        onSubmit={_handleSubmit}
      />
    ),
    [_handleSubmit, isSubmitDisabled, onCancel, id],
  );

  location;
  if (loading) {
    return <Spinner inline={false} />;
  }

  return (
    <ContentBox className="right-panel">
      <FormTitle className="no-margin-top" title={title} />
      <Tabs horizontal saveTabs tabStyle="no-margin-bottom">
        <Tabs.Tab default route="details" title={t('Details')}>
          <div className="pt-10">
            <EntityForm
              entity={entity}
              entityName={uid}
              hasValidateOnBlur={false}
              hasValidateOnChange={false}
              submitSectionRenderer={submitSectionRenderer}
              validateOnMount={false}
              onCancel={onCancel}
              onLeave={saveData}
              onSubmit={_handleSubmit}
            >
              <EntityFormFieldSet>
                <EntityNameField
                  required
                  label={t('#{name} Name', { name: programActivityName })}
                />
                <CodeField maxLength={MAX_CODE_LENGTH} />
                <SubjectLevelTreeField
                  isRequired
                  excludeId={selectedNode?.id}
                  programGroupId={programGroupId}
                  syntheticRootNodeName={`${entityName} ${programActivityName} tree`}
                />
                <IconSelectorField
                  columns={3}
                  label={t('Icon')}
                  name="icon"
                  wrapperClassNames={{
                    3: classNames(
                      `col-lg-4 col-md-6 col-sm-12 ${styles.mb10}`,
                      styles.mbIcon,
                    ),
                  }}
                />
                <ColourField
                  required
                  columns={3}
                  label={t('Colour')}
                  name="colour"
                />
                <SequenceField
                  required
                  error={subjectSequenceError}
                  loading={subjectSequenceLoading}
                />
                <StatusField
                  required
                  defaultValue={Statuses.Active.value}
                  isNew={!id}
                  label={t('Status')}
                  name="status"
                />
              </EntityFormFieldSet>
              <AttachmentsWithCaptionField
                hasCaption
                addButtonTitle={t('Attach File')}
                cardsInRow={4}
                categoryKey={SUBJECT_ATTACHMENTS}
                columns={1}
                hasPhotoRecorder={false}
                maxDescriptionLength={MAX_DESCRIPTION_LENGTH}
                name="subjectAttachments"
                strikeTitle={t('Attachment')}
              />
            </EntityForm>
          </div>
        </Tabs.Tab>

        <Tabs.Tab disabled={!id} route="lms" title={t('LMS')}>
          <div className="pt-10">
            <EntityForm
              entity={entity}
              entityName={uid}
              hasValidateOnBlur={false}
              hasValidateOnChange={false}
              submitSectionRenderer={submitSectionRenderer}
              validateOnMount={false}
              onCancel={onCancel}
              onLeave={saveData}
              onSubmit={_handleSubmit}
            >
              <ProgramGroupCatalogueSubjectLms
                programGroupTaskTypes={programGroupTaskTypes}
                programGroupTypeId={groupTypeId}
              />
            </EntityForm>
          </div>
        </Tabs.Tab>

        <Tabs.Tab disabled={!id} route="resources" title={t('Resources')}>
          <ProgramGroupCatalogueSubjectResources subjectId={entity?.id} />
        </Tabs.Tab>

        <Tabs.Tab disabled={!id} route="schedule" title={t('Schedule')}>
          <div className="pt-10">
            <EntityForm
              entity={entity}
              entityName={uid}
              hasValidateOnBlur={false}
              hasValidateOnChange={false}
              submitSectionRenderer={submitSectionRenderer}
              validate={handleValidate}
              validateOnMount={false}
              onCancel={onCancel}
              onLeave={saveData}
              onSubmit={_handleSubmit}
            >
              <ProgramGroupCatalogueSubjectSchedule
                programGroupTypeId={groupTypeId}
              />
            </EntityForm>
          </div>
        </Tabs.Tab>
      </Tabs>
    </ContentBox>
  );
};

export default ProgramGroupCatalogueSubjectForm;
