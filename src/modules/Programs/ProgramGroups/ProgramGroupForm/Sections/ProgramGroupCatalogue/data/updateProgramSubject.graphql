#import './FileAttachmentMainFragment.graphql'

mutation updateProgramSubject($id: Int!, $params: ProgramSubjectInput!) {
  updateProgramSubject(id: $id, params: $params) {
    __typename
    id
    name
    code
    status
    parentId
    programGroupId
    orgGroupId
    markBook
    gradeScaleId
    attendanceCodeSet
    attendanceCodeSetId
    submissions
    learningPlan
    colour
    isScheduled
    icon
    subjectAttachments {
      ...FileAttachmentMain
    }

    sessionHoursCredits
    timetable

    programGroupSessions {
      id
      creditRatio
      minutes
      per
      programGroupSessionId
      credits
    }
    timetables {
      id
      periodNum
      periodType
    }
    sequence
  }
}
