import React, { use<PERSON><PERSON>back, useContext, useMemo } from 'react';
import { useHistory, useRouteMatch } from 'react-router-dom';
import { map } from 'lodash';

import { TLearningPlanStatus } from '../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILearningPlan';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import LearningPlanStatusField from '../../../../../../../common/components/forms/LearningTaskPlanTree/LearningPlanStatusField';
import Box, {
  BoxSizeWrapper,
} from '../../../../../../../common/components/other/Box';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import LearningPlanStatus from '../../../../../../../model/LearningPlanStatus';
import StructureSubjectLevelTreeField, {
  isProgramStructureSubject,
} from '../fields/StructureSubjectLevelTreeField';
import TreeContext from '../../../../../../../common/components/dataViews/NextTree/TreeContext';
import { isRootNode } from '../../../../../../../common/components/dataViews/DynamicTree';
import {
  IProgram,
  TProgramStructureTree,
} from '../../../../../../../model/ProgramGroupStructureTypeNames';

interface ILearningPlan {
  id?: number;
  name: string;
  learningStatus: TLearningPlanStatus;
  subjectId: number;
  isSubmissionEnabled?: boolean;
  tasksCount?: [{ status: TLearningPlanStatus; count: number }];
  activityName: string;
}

const LearningPlanForm: React.FC<{
  title: string;
  onSubmit: (values: ILearningPlan) => void;
  node?: ILearningPlan;
  onCancel: () => void;
  parentNode?: { id: number };
  programGroupId: number;
  rootName: string;
  syntheticRootNodeName: string;
  programName: number;
  programs?: IProgram[];
  activityName: string;
}> = ({
  onCancel,
  title,
  node,
  onSubmit,
  parentNode,
  programGroupId,
  rootName,
  syntheticRootNodeName,
  programs,
  programName,
  activityName,
}) => {
  const t = useT();
  const { push } = useHistory();
  const { url } = useRouteMatch();
  const tasksCount = useMemo(() => node?.tasksCount || [], [node]);

  const {
    treeState: { inputModels },
  } = useContext(TreeContext);

  const parentLevel = useMemo(() => {
    if (!node?.subjectId) {
      return (
        inputModels.find(
          (model: TProgramStructureTree) =>
            model.id === parentNode?.id && isProgramStructureSubject(model),
        ) || inputModels.find(isRootNode)
      );
    }

    return inputModels.find(
      (model: TProgramStructureTree) =>
        model.id === node.subjectId && isProgramStructureSubject(model),
    );
  }, [parentNode, node, inputModels]);

  const _entity = useMemo(
    () =>
      node && node.id
        ? { ...node, parentLevel }
        : {
            learningStatus: LearningPlanStatus.Draft.value,
            name: '',
            subjectId: parentNode?.id as number,
            parentLevel,
          },
    [node, parentNode, parentLevel],
  );

  const _onSubmit = useCallback(
    ({ parentLevel, ...values }) =>
      onSubmit({
        id: values.id,
        name: values.name,
        learningStatus: values.learningStatus,
        subjectId: parentLevel?.id || values.subjectId,
        activityName,
      }),
    [onSubmit, activityName],
  );

  const handleTasksClick = useCallback(() => {
    push(`${url}/tasks`);
  }, [url, push]);

  const isNew = useMemo(() => !node?.id, [node]);
  return (
    <>
      <EntityForm
        columns={4}
        entity={_entity as any}
        entityName="LearningPlan"
        onCancel={isNew ? onCancel : undefined}
        onGoBack={isNew ? undefined : onCancel}
        onSubmit={_onSubmit}
      >
        <EntityFormFieldSet>
          <EntityNameField columns={3} label={t('#{title} Name', { title })} />
          <StructureSubjectLevelTreeField
            isRequired
            programGroupId={programGroupId}
            programName={programName}
            programs={programs}
            rootName={rootName}
            syntheticRootNodeName={syntheticRootNodeName}
          />
          <LearningPlanStatusField
            omitCompleted
            columns={3}
            label={t('Status')}
          />
        </EntityFormFieldSet>
      </EntityForm>

      <BoxSizeWrapper>
        <div className="row mt-10">
          <Box title={t('Tasks')} onClick={handleTasksClick}>
            {tasksCount &&
              tasksCount.length > 0 &&
              map(
                tasksCount
                  .slice()
                  .sort(
                    (a, b) =>
                      LearningPlanStatus.BasicByValue[a.status]?.id -
                      LearningPlanStatus.BasicByValue[b.status]?.id,
                  ),
                (item, key) => (
                  <div key={key}>
                    {t(`#{count} #{task} (#{status})`, {
                      count: item.count,
                      task: item.count > 1 ? 'tasks' : 'task',
                      status:
                        LearningPlanStatus.BasicByValue[item.status]?.name ||
                        '',
                    })}
                  </div>
                ),
              )}
            {tasksCount.length === 0 && <div>{t('0 task')}</div>}
          </Box>
        </div>
      </BoxSizeWrapper>
    </>
  );
};

export default LearningPlanForm;
