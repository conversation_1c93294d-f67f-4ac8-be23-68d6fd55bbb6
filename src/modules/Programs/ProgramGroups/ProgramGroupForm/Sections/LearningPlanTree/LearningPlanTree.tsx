import { get, includes, isEqual, map, some, startsWith, sumBy } from 'lodash';
import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';
import { useQuery } from 'react-apollo';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import { TreeLayoutTypeEnum } from '../../../../../../common/components/dataViews/BasicTrees/TreeLayoutConfig';
import GqlFullCrudTree from '../../../../../../common/components/dataViews/GqlFullCrudTree';
import { catsearch } from '../../../../../../common/textSearch';
import getGqlOperationName from '../../../../../../common/utils/getGqlOperationName';
import nodeSortingOptionsSequence from '../../../../../../common/utils/nodeSortingOptionsSequence';
import LearningPlanStatus, {
  Deleted,
} from '../../../../../../model/LearningPlanStatus';
import {
  isProgram,
  isProgramFolder,
} from '../../../../../../model/ProgramGroupProgramTypeNames';
import programSelector from '../../../data/programSelector.graphql';
import createLearningPlan from './data/createLearningPlan.graphql';
import deleteLearningPlan from './data/deleteLearningPlan.graphql';
import learningPlanGql from './data/learningPlan.graphql';
import learningPlanTree from './data/learningPlanTree.graphql';
import subject from './data/subject.graphql';
import updateLearningPlan from './data/updateLearningPlan.graphql';
import LearningPlanTreeFilter from './filter/LearningPlanTreeFilter';
import LearningPlanEditForm from './form/LearningPlanEditForm';
import ProgramSubjectEditForm from './form/ProgramSubjectEditForm';
import { isRootNode } from '../../../../../../common/components/dataViews/DynamicTree';
import LearningPlanForm from './form/LearningPlanForm';
import TasksCount from './addon/TasksCount';
import useT from '../../../../../../common/components/utils/Translations/useT';
import { IProgramGroupTypeSynonym } from '../../../../../../common/data/hooks/useProgramGroupTypes';
import { EditSessionButton } from '../../../../../../common/components/containers/EditSessionProvider';
import ContentPanel from '../../../../../../common/components/containers/BasicModuleLayout/ContentPanel';
import StatusAddon from '../../../../../../common/components/dataViews/NextTree/addons/StatusAddon';
import { isActive } from '../../../../../../model/Statuses';
import useFilterStore from '../../../../../../common/components/controls/FilterBar/hooks/useFilterStore';

const FILTER_KEY = 'LEARNING_PLAN';
const defaultFilter = {
  programs: [],
  learningStatus: LearningPlanStatus.BasicDefault,
  searchQuery: '',
  view: 'SUBJECTS_WITH_LP',
};

const LearningPlanTree: FC<ILearningPlanTreeProps> = ({
  programGroupId,
  programGroupName,
  programGroupType: {
    id: programGroupTypeId,
    activityName,
    programName,
    programNamePlural,
    learningPlan,
    learningPlanPlural,
  },
}) => {
  const t = useT();
  const { url } = useRouteMatch();
  const { push } = useHistory();

  const [filter, setFilter] = useState(defaultFilter);

  const handleFilterChange = useCallback(
    val => setFilter(values => ({ ...values, ...val })),
    [setFilter],
  );

  const queryRes = useQuery(programSelector, {
    variables: { programGroupId },
    skip: !programGroupId,
    onCompleted: data => {
      const programs = get(data, getGqlOperationName(programSelector));
      setFilter(currentFilter => ({
        ...currentFilter,
        programs:
          currentFilter.programs?.length > 0
            ? currentFilter.programs
            : programs,
      }));
    },
  });

  const programs = useMemo(
    () => get(queryRes, `data.${getGqlOperationName(programSelector)}`, []),
    [queryRes],
  );

  const {
    values: defaultFilterValues = {
      programs: [],
      learningStatus: LearningPlanStatus.BasicDefault,
      searchQuery: '',
      view: 'SUBJECTS_WITH_LP',
    },
  } = useFilterStore(`${FILTER_KEY}_${programGroupId}`, {
    defaultValues: {
      programs: [],
      learningStatus: LearningPlanStatus.BasicDefault,
      searchQuery: '',
      view: 'SUBJECTS_WITH_LP',
    },
  });

  useEffect(() => {
    if (defaultFilterValues) {
      setFilter(values => ({
        ...values,
        ...defaultFilterValues,
      }));
    }
  }, [defaultFilterValues]);

  const filterProgramIds = useMemo(
    () => map(filter.programs?.filter(isProgram), 'id'),
    [filter.programs],
  );

  const filterProgramFolderIds = useMemo(
    () => map(filter.programs?.filter(isProgramFolder), 'id'),
    [filter.programs],
  );

  const makeProgramsMatcher = useCallback(
    () => node => {
      if (isProgram(node)) {
        return includes(filterProgramIds, node.id);
      }
      if (isProgramFolder(node)) {
        return includes(filterProgramFolderIds, node.id);
      }
      if (isProgramStructureSubject(node)) {
        return includes(filterProgramIds, node.programId);
      }
      return false;
    },
    [filterProgramFolderIds, filterProgramIds],
  );

  const makeStatusMatcher = useCallback(
    () =>
      filter.learningStatus
        ? ({ learningStatus }) => {
            if (!learningStatus) return true;
            if (
              isEqual(
                filter.learningStatus,
                LearningPlanStatus.BasicWithoutCompletedProps,
              )
            ) {
              return (
                learningStatus === undefined ||
                includes(filter.learningStatus, learningStatus)
              );
            }
            return includes(filter.learningStatus, learningStatus);
          }
        : null,
    [filter.learningStatus],
  );

  const makeNameQueryMatcher = useCallback(
    () =>
      filter.searchQuery
        ? node => {
            const { name } = node;
            return !isRootNode(node) && catsearch(name, filter.searchQuery);
          }
        : null,
    [filter.searchQuery],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'learningStatus',
        predicate: makeStatusMatcher,
        includeChildren: false,
        collectVisibleIds:
          !!filter.learningStatus?.length && !filter.searchQuery,
      },
      {
        valuePropName: 'searchQuery',
        predicate: makeNameQueryMatcher,
        includeChildren: true,
        collectVisibleIds: !!filter.searchQuery,
      },
      {
        valuePropName: 'programs',
        predicate: makeProgramsMatcher,
        includeChildren: true,
        includeParents: true,
        collectVisibleIds: !filter.searchQuery,
      },
    ],
    [
      filter.learningStatus,
      filter.searchQuery,
      makeNameQueryMatcher,
      makeProgramsMatcher,
      makeStatusMatcher,
    ],
  );

  const onCancel = useCallback(() => push(url), [push, url]);

  const renderEdit = useCallback(
    ({ match: { params } }) => (
      <LearningPlanEditForm
        activityName={String(activityName)}
        id={Number(params.id)}
        programGroupId={Number(programGroupId)}
        programGroupTypeId={programGroupTypeId}
        query={learningPlanGql}
        title={t(String(learningPlan))}
        onCancel={onCancel}
      />
    ),
    [
      programGroupTypeId,
      activityName,
      learningPlan,
      onCancel,
      t,
      programGroupId,
    ],
  );

  const renderNodeAddons = useCallback(
    ({ id, name, tasksCount, learningStatus }) => [
      <TasksCount
        key={`tasks_count_${name}_${id}`}
        tasksCount={sumBy(tasksCount, 'count') || 0}
      />,
      <StatusAddon
        key={`status_${id}`}
        customDeletedStatus={Deleted}
        customStatuses={LearningPlanStatus.TreeStatusesByValue}
        status={learningStatus}
      />,
    ],
    [],
  );

  return (
    <ContentPanel
      rightActionButton={<EditSessionButton />}
      title={t(learningPlanPlural)}
    >
      <Switch>
        <Route
          path={`${url}/:id/edit/learning-plan/tasks`}
          render={renderEdit}
        />
        <Route path={`${url}`}>
          <GqlFullCrudTree
            nodes={{
              folder: {
                isThisNodeType: isProgramFolder,
                canCreateUnder: canCreateUnderFalse,
              },
              program: {
                isThisNodeType: isProgram,
                canCreateUnder: canCreateUnderFalse,
              },
              subject: {
                title: activityName && t(activityName),
                isThisNodeType: isProgramStructureSubject,
                canCreateUnder: canCreateUnderFalse,
                component: ProgramSubjectEditForm,
                gql: {
                  single: subject,
                },
              },
              'learning-plan': {
                title: learningPlan && learningPlan,
                isNodeDeleted: LearningPlanStatus.isDeleted,
                isThisNodeType: isLearningPlan,
                canCreateUnder: isProgramStructureSubject,
                component: LearningPlanForm,
                renderNodeAddons,
                programGroupId,
                rootName: `${programGroupName} ${learningPlan}`,
                programName: programName && t(programName),
                syntheticRootNodeName: t('#{group} #{name}', {
                  group: programGroupName,
                  name: programNamePlural && t(programNamePlural),
                }),
                programs,
                activityName,
                gql: {
                  single: learningPlanGql,
                  getParentGql: () => subject,
                  create: createLearningPlan,
                  update: updateLearningPlan,
                  delete: deleteLearningPlan,
                },
              },
            }}
            tree={{
              title: learningPlanPlural && t(learningPlanPlural),
              rootName: `${programGroupName} ${learningPlan}`,
              key: 'programs',
              gql: {
                query: learningPlanTree,
                variables: { programGroupId, view: filter.view },
                skip: !programGroupId,
              },
              treeLayoutType: TreeLayoutTypeEnum.TREE_TABBED_PAGE,
              adapter: {
                resolveNodeId,
                resolveNodeParentId,
                nodeIsLeaf: isLearningPlan,
                canRender,
              },
              plugins: {
                nodeIsSelectable,
                nodeSortingOptions: nodeSortingOptionsSequence,
              },
              filter: {
                value: filter,
                onChange: handleFilterChange,
                component: LearningPlanTreeFilter,
                predicatesMeta,
                props: {
                  programGroupTypeId,
                  programName: programName && t(programName),
                  programGroupId,
                  syntheticRootNodeName: t('#{group} #{name}', {
                    group: programGroupName,
                    name: programNamePlural && t(programNamePlural),
                  }),
                  programs,
                },
              },
            }}
          />
        </Route>
      </Switch>
    </ContentPanel>
  );
};

export default LearningPlanTree;

interface ILearningPlanTreeProps {
  orgGroupId: number;
  programGroupId?: number;
  programGroupName?: string;
  programGroupType: IProgramGroupTypeSynonym;
}

const ProgramFolder = 'ProgramFolder';
const Program = 'Program';
const ProgramStructureSubject = 'ProgramStructureSubject';
const LearningPlan = 'LearningPlan';

const isProgramStructureSubject = ({ __typename }) =>
  __typename === ProgramStructureSubject;
const isLearningPlan = ({ __typename }) => __typename === LearningPlan;

const resolveNodeId = node => {
  if (isProgramFolder(node)) {
    return `${ProgramFolder}-${node.id}`;
  }
  if (isProgram(node)) {
    return `${Program}-${node.id}`;
  }
  if (isProgramStructureSubject(node)) {
    return `${ProgramStructureSubject}-${node.id}`;
  }
  if (isLearningPlan(node)) {
    return `${LearningPlan}-${node.id}`;
  }

  return node.id;
};

const resolveNodeParentId = node => {
  if (isProgramFolder(node) && node.parentId) {
    return `${ProgramFolder}-${node.parentId}`;
  }
  if (isProgram(node) && node.programFolderId) {
    return `${ProgramFolder}-${node.programFolderId}`;
  }
  if (isProgramStructureSubject(node) && node.programId) {
    return `${Program}-${node.programId}`;
  }
  if (isLearningPlan(node)) {
    return `${ProgramStructureSubject}-${node.subjectId}`;
  }

  return node.parentId;
};

const nodeIsSelectable = node =>
  isLearningPlan(node) || isProgramStructureSubject(node);

const canCreateUnderFalse = () => false;

const canRender = ({ id, model, leaf, nodesMeta: { hasChild }, state }) => {
  const checkLeafExistsRecursively = (nodeId: string): boolean => {
    if (!state.children[nodeId] || !state.children[nodeId].length) {
      return false;
    }

    const leafExists = some(state.children[nodeId], str =>
      startsWith(str, `${LearningPlan}-`),
    );

    if (leafExists) {
      return true;
    }

    return some(state.children[nodeId], childId =>
      checkLeafExistsRecursively(childId),
    );
  };

  if (
    (model.id !== ':ROOT:' || model.id !== null) &&
    !isLearningPlan(model) &&
    (isProgram(model) || !isActive(model))
  ) {
    if (state.children[id]) {
      const leafExists = checkLeafExistsRecursively(id);

      if (!leafExists || leaf) {
        return false;
      }
    } else {
      return false;
    }
  }
  return true;
};
