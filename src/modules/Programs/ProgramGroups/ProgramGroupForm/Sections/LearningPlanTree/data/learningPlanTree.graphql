#import './ProgramFragmentShort.graphql'
#import './ProgramStructureSubjectFragment.graphql'
#import './LearningPlanFragment.graphql'

query learningPlanTree(
  $programGroupId: Int!
  $view: String
) {
  learningPlanTree(
    programGroupId: $programGroupId
    view: $view
  ) {
    ... on ProgramFolder {
      id
      name
      status
      parentId
      sequence
    }

    ... on Program {
      ...ProgramFragmentShort
    }

    ... on ProgramStructureSubject {
      ...ProgramStructureSubjectFragment
      subjectId
    }

    ... on LearningPlan {
      ...LearningPlanFragment
    }
  }
}
