#import './ProgramFragmentShort.graphql'
#import './ProgramStructureSubjectFragment.graphql'

query learningPlanTree($programGroupId: Int!) {
  learningPlanTree(programGroupId: $programGroupId) {
    ... on ProgramFolder {
      id
      name
      status
      parentId
      sequence
    }

    ... on Program {
      ...ProgramFragmentShort
    }

    ... on ProgramStructureSubject {
      ...ProgramStructureSubjectFragment
      subjectId
    }
  }
}
