import React from 'react';
import useT from '../../../../../../../common/components/utils/Translations/useT';

export interface ITasksCount {
  tasksCount: number;
  className?: string;
}
const TasksCount: React.FC<ITasksCount> = ({
  tasksCount,
  className = 'display-inline-block ml-5 text-italic',
}) => {
  const t = useT();
  if (!tasksCount) {
    return null;
  }

  return (
    <p
      className={className}
      title={t('Number of Tasks')}
    >{`(${tasksCount})`}</p>
  );
};

export default TasksCount;
