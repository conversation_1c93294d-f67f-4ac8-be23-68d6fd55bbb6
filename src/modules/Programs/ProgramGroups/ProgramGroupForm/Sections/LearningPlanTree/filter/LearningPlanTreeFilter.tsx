import React, { useCallback, useMemo, useState } from 'react';
import { map, filter } from 'lodash';

import SearchField from '../../../../../../../common/components/controls/base/SearchField';
import FilterBar from '../../../../../../../common/components/controls/FilterBar';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import { TLearningPlanStatus } from '../../../../../../../common/abstract/OrganisationGroup/ProgramGroups/ILearningPlan';
import { IProgram } from '../../../../../../../common/abstract/Program/IProgram';
import { isProgram } from '../../../../../../../model/ProgramGroupProgramTypeNames';
import useProgramGroupTypes from '../../../../../../../common/data/hooks/useProgramGroupTypes';

export interface ILearningPlanTreeFilter {
  programGroupTypeId: number;
  value: {
    status: TLearningPlanStatus;
    searchQuery: string;
  };
  onChange: (values) => void;
  programGroupId: number;
  programName: number;
  syntheticRootNodeName: string;
  programs?: IProgram[];
  searchFieldWidthClassName: string;
}
const FILTER_KEY = 'LEARNING_PLAN';

const LearningPlanTreeFilter: React.FC<ILearningPlanTreeFilter> = ({
  programGroupTypeId,
  value,
  onChange,
  programGroupId,
  programName,
  syntheticRootNodeName,
  programs = [],
  searchFieldWidthClassName,
}) => {
  const t = useT();
  const { getTitle } = useProgramGroupTypes(programGroupTypeId);
  const activityNamePlural = getTitle('activityNamePlural');

  const [searchQuery, setSearchQuery] = useState(value.searchQuery);

  const handleChange = useCallback(
    ({ learningStatus, programs, view }) =>
      onChange && onChange({ learningStatus, programs, view }),
    [onChange],
  );

  const handleSearchQueryChange = useCallback(
    searchQuery => {
      onChange && onChange({ searchQuery });
      setSearchQuery(searchQuery);
    },
    [setSearchQuery, onChange],
  );

  const handleSearchInputFocus = useCallback(searchContainerInput => {
    if (!searchContainerInput) {
      return;
    }

    const input = searchContainerInput.getElementsByTagName('input')[0];
    input && input.focus();
  }, []);

  const values = useMemo(() => ({ ...value, searchQuery }), [
    searchQuery,
    value,
  ]);

  const prepareFilterValues = useCallback(
    ({ programs = [], ...values }) => ({
      ...values,
      programs: map(filter(programs, isProgram), ({ id, name }) => ({
        id,
        name,
      })),
    }),
    [],
  );

  const restoreFilterValues = useCallback(
    ({ programs = [], ...values }) => ({
      ...values,
      programs: map(programs, ({ id, name }) => ({
        id,
        name,
        __typename: 'Program',
      })),
    }),
    [],
  );

  return (
    <>
      <FilterBar
        filterKey={`${FILTER_KEY}_${programGroupId}`}
        searchName="searchQuery"
        values={values}
        onBeforeComplete={restoreFilterValues}
        onBeforeUpdate={prepareFilterValues}
        onChange={handleChange}
      >
        <FilterBar.ProgramTreeSelector
          hasFilter
          hasSearchFieldOnly={false}
          name="programs"
          programGroupId={programGroupId}
          programs={programs}
          syntheticRootNodeName={syntheticRootNodeName}
          title={programName}
        />
        <FilterBar.LPViewSelector
          activityNamePlural={activityNamePlural}
          name="view"
        />
        <FilterBar.LearningPlanStatusSelector
          omitCompleted
          name="learningStatus"
        />
      </FilterBar>

      <div ref={handleSearchInputFocus}>
        <SearchField
          placeholder={t('Search')}
          searchProps={{ isChangeRaisedOnBlur: false }}
          value={searchQuery}
          widthClassName={searchFieldWidthClassName}
          onChange={handleSearchQueryChange}
        />
      </div>
    </>
  );
};

export default LearningPlanTreeFilter;
