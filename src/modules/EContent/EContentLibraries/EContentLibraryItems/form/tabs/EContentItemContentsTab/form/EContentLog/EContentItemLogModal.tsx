import React, { useCallback, useMemo } from 'react';
import { isEmpty } from 'lodash';

import useT from '../../../../../../../../../common/components/utils/Translations/useT';
import Modal from '../../../../../../../../../common/components/utils/Modal';
import EntityForm from '../../../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import TextAreaField from '../../../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import { IEContentItemLog } from '../../EContentItemLogTab';
import { ManualLog } from '../../../../../../../../../model/EContentLogType';

export interface ILogModal {
  item: IEContentItemLog | null;
  isNew: boolean;
  isModalVisible: boolean;
  onDelete: (item: IEContentItemLog) => Promise<void>;
  onUpdate: (item: IEContentItemLog) => Promise<void>;
  onSubmit: (item: IEContentItemLog) => Promise<void>;
  setModalVisibility: (isVisible: boolean) => void;
}

export default function EContentItemLogModal({
  item,
  isNew,
  isModalVisible,
  onSubmit,
  onDelete,
  onUpdate,
  setModalVisibility,
}: ILogModal) {
  const t = useT();

  const _entity = useMemo(
    () => ({
      description: '',
      logTypeId: ManualLog.id,
      ...item,
    }),
    [item],
  );

  const handleModalClose = useCallback(() => {
    setModalVisibility(false);
  }, [setModalVisibility]);

  const handleDelete = useCallback(() => {
    item && onDelete(item);
  }, [item, onDelete]);

  const handleIsSubmitDisabled = useCallback(
    (isDisabled: boolean, formikBag: any) =>
      isEmpty(formikBag.values?.description),
    [],
  );

  return (
    <Modal
      title={isNew ? t('Add Manual Log') : t('Update Manual Log')}
      visible={isModalVisible}
      onClose={handleModalClose}
    >
      <EntityForm
        createLabel={t('Add')}
        deleteLabel="Remove"
        entity={_entity}
        isNew={isNew}
        isSubmitDisabled={handleIsSubmitDisabled}
        submitClass="no-padding pt-10"
        successMessageText="Added Successfully"
        updateLabel={t('Update')}
        onCancel={handleModalClose}
        onDelete={handleDelete}
        onSubmit={isNew ? onSubmit : onUpdate}
      >
        <div>
          <EntityFormFieldSet>
            <TextAreaField
              autoHeight
              required
              columns={1}
              label={t('Log Details')}
              maxLength={250}
              name="description"
              placeholder={t('Add Log Details')}
              rows={10}
            />
          </EntityFormFieldSet>
        </div>
      </EntityForm>
    </Modal>
  );
}
