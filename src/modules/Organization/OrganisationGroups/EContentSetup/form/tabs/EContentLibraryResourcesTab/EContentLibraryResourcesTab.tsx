import React, { useCallback, useMemo, useState } from 'react';
import { includes } from 'lodash';
import { useHistory, useRouteMatch } from 'react-router-dom';

import { IEContentLibraryForm } from '../../EContentLibraryForm';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import createEContentResource from '../../../../../../../common/data/eContent/createEContentResource.graphql';
import updateEContentResource from '../../../../../../../common/data/eContent/updateEContentResource.graphql';
import deleteEContentResource from '../../../../../../../common/data/eContent/deleteEContentResource.graphql';
import eContentResource from '../../../../../../../common/data/eContent/eContentResource.graphql';
import createEContentFolder from '../../../../../../../common/data/eContent/createEContentFolder.graphql';
import updateEContentFolder from '../../../../../../../common/data/eContent/updateEContentFolder.graphql';
import deleteEContentFolder from '../../../../../../../common/data/eContent/deleteEContentFolder.graphql';
import eContentFolder from '../../../../../../../common/data/eContent/eContentFolder.graphql';
import eContentLibraryResourcesTree from '../../../../../../../common/data/eContent/eContentLibraryResourcesTree.graphql';
import eContentLibrary from '../../../../../../../common/data/eContent/eContentLibrary.graphql';
import GqlFullCrudTree from '../../../../../../../common/components/dataViews/GqlFullCrudTree';
import { TreeLayoutTypeEnum } from '../../../../../../../common/components/dataViews/BasicTrees/TreeLayoutConfig';
import { isRootNode } from '../../../../../../../common/components/dataViews/DynamicTree';
import EContentFolderForm from './EContentFolderForm';
import StatusWithDraft, {
  Active,
  Deleted,
  TStatusWithDraft,
} from '../../../../../../../model/StatusWithDraft';
import nodeSortingOptionsSequence from '../../../../../../../common/utils/nodeSortingOptionsSequence';
import {
  isEContentLibraryFolder,
  isEContentLibraryResource,
  resolveNodeId,
  resolveNodeParentId,
} from '../../../../../../../model/EContentLibraryResourcesTypeNames';
import { catsearch } from '../../../../../../../common/textSearch';
import EContentResourceForm from './EContentResourceForm';
import FilterBar from '../../../../../../../common/components/controls/FilterBar';
import SearchField from '../../../../../../../common/components/controls/base/SearchField';
import { IActionsAddonAction } from '../../../../../../../common/components/dataViews/NextTree/addons/ActionsAddon/ActionsAddon';

interface ITreeFilterBar {
  status: TStatusWithDraft[];
}

const EContentLibraryResourcesTab: React.FC<IEContentLibraryForm> = ({
  entity,
}) => {
  const t = useT();
  const history = useHistory();
  const { url } = useRouteMatch();

  const organisationGroupId = useMemo(() => entity.organisationGroupId, [
    entity,
  ]);

  const [filters, setFilters] = useState({
    status: [Active.value],
    searchQuery: '',
  });

  const _handleChangeSearch = useCallback(
    searchValue => {
      setFilters({
        ...filters,
        searchQuery: searchValue,
      });
    },
    [filters, setFilters],
  );

  const renderFilter = useCallback(
    ({ value, onChange }) => (
      <div className="mb-20">
        <FilterBar<ITreeFilterBar> values={value} onChange={onChange}>
          <FilterBar.StatusWithDraftMultiSelector name="status" />
        </FilterBar>
        <SearchField
          placeholder={t('Search')}
          value={filters.searchQuery}
          onChange={_handleChangeSearch}
        />
      </div>
    ),
    [t, _handleChangeSearch, filters.searchQuery],
  );

  const variables = useMemo(
    () => ({
      status: filters.status,
      searchQuery: filters.searchQuery,
      libraryId: entity.id,
      treeDataMode: 'EDIT',
    }),
    [filters, entity],
  );

  const makeStatusMatcher = useCallback(
    () =>
      filters.status
        ? ({ status }) =>
            status === undefined || includes(filters.status, status)
        : null,
    [filters.status],
  );

  const makeNameQueryMatcher = useCallback(
    () =>
      filters.searchQuery
        ? ({ name }) =>
            name === undefined || catsearch(name, filters.searchQuery)
        : null,
    [filters.searchQuery],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'status',
        predicate: makeStatusMatcher,
        includeChildren: false,
        collectVisibleIds: !!filters.status?.length && !filters.searchQuery,
      },
      {
        valuePropName: 'searchQuery',
        predicate: makeNameQueryMatcher,
        includeChildren: true,
        collectVisibleIds: !!filters.searchQuery,
      },
    ],
    [
      filters.status,
      filters.searchQuery,
      makeNameQueryMatcher,
      makeStatusMatcher,
    ],
  );

  const handleAddSubject = useCallback(
    setSelectedNode => node => {
      history.push(`${url}/add/resource`);
      setSelectedNode(node);
    },
    [history, url],
  );

  const renderNodeActions = useCallback(
    (node, { isEditSessionActive }, { setSelectedNode }) => {
      const actions: IActionsAddonAction[] = [];
      if (isEditSessionActive && isEContentLibraryResource(node)) {
        actions.push({
          id: `copy_resource_${node.id}`,
          title: t('Copy'),
          action: handleAddSubject(setSelectedNode),
        });
      }
      return actions;
    },
    [t, handleAddSubject],
  );

  return (
    <GqlFullCrudTree
      nodes={{
        folder: {
          title: 'Folder',
          isThisNodeType: isEContentLibraryFolder,
          canCreateUnder: node =>
            isRootNode(node) || isEContentLibraryFolder(node),
          component: EContentFolderForm,
          gql: {
            single: eContentFolder,
            create: createEContentFolder,
            update: updateEContentFolder,
            delete: deleteEContentFolder,
          },
          libraryId: entity?.id,
          libraryName: entity?.name,
          organisationGroupId,
        },
        resource: {
          title: 'Resource',
          isThisNodeType: isEContentLibraryResource,
          canCreateUnder: node =>
            isRootNode(node) || isEContentLibraryFolder(node),
          component: EContentResourceForm,
          gql: {
            single: eContentResource,
            create: createEContentResource,
            update: updateEContentResource,
            delete: deleteEContentResource,
            getParentGql: node =>
              isEContentLibraryFolder(node) ? eContentFolder : eContentLibrary,
          },
          library: entity,
          libraryId: entity?.id,
          libraryName: entity?.name,
          organisationGroupId,
          renderNodeActions,
        },
      }}
      tree={{
        key: 'EContentLibraryResources',
        rootName: entity.name,
        gql: {
          query: eContentLibraryResourcesTree,
          variables,
        },
        filter: {
          component: renderFilter,
          value: filters,
          onChange: setFilters,
          hasDraftStatus: true,
          predicatesMeta,
        },
        adapter: {
          resolveNodeParentId,
          resolveNodeId,
          nodeIsLeaf: isEContentLibraryResource,
        },
        plugins: {
          addNodeBuildMeta,
          nodeSortingOptions: nodeSortingOptionsSequence,
        },
        treeLayoutType: TreeLayoutTypeEnum.TREE_TABBED_PAGE,
        title: t('Resources'),
        hasTitle: true,
        customStatuses: StatusWithDraft.BasicByValue,
        customActiveStatus: Active,
        customDeletedStatus: Deleted,
      }}
    />
  );
};

export default EContentLibraryResourcesTab;

const addNodeBuildMeta = (node, parentMeta) => {
  if (parentMeta) {
    parentMeta.hasChild = true;

    if (node.status === Active.value) {
      parentMeta.hasActiveChild = true;
    }
  }

  return { hasChild: false, hasActiveChild: false };
};
