import { get, includes, some, map, isEmpty } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useMutation, useQuery } from 'react-apollo';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';

import styles from './LearningPlanTree.scss';

import { TreeLayoutTypeEnum } from '../../../../../common/components/dataViews/BasicTrees/TreeLayoutConfig';
import ContentPanel from '../../../../../common/components/containers/BasicModuleLayout/ContentPanel';
import { isRootNode } from '../../../../../common/components/dataViews/DynamicTree';
import GqlFullCrudTree from '../../../../../common/components/dataViews/GqlFullCrudTree';
import ActionsAddon from '../../../../../common/components/dataViews/NextTree/addons/ActionsAddon';
import CategoryEditForm from '../../../../../common/components/forms/LearningTaskPlanTree/CategoryEditForm';
import TaskEditForm from '../../../../../common/components/forms/LearningTaskPlanTree/TaskEditForm';
import Breadcrumbs from '../../../../../common/components/utils/Breadcrumbs';
import { extractErrorMessage } from '../../../../../common/components/utils/Error';
import LoadingMask from '../../../../../common/components/utils/LoadingMask';
import useT from '../../../../../common/components/utils/Translations/useT';
import DeleteAddon from '../../../../../common/components/dataViews/NextTree/addons/DeleteAddon';
import createCategoryGql from '../../../../../common/data/LearningTaskPlanTree/createCategory.graphql';
import deleteCategoryGql from '../../../../../common/data/LearningTaskPlanTree/deleteCategory.graphql';
import learningPlanCategoryGql from '../../../../../common/data/LearningTaskPlanTree/learningPlanCategory.graphql';
import learningPlanTaskGql from '../../../../../common/data/LearningTaskPlanTree/learningPlanTask.graphql';
import updateCategoryGql from '../../../../../common/data/LearningTaskPlanTree/updateCategory.graphql';
import { handleFormErrors } from '../../../../../common/errors';
import { catsearch } from '../../../../../common/textSearch';
import Notifications from '../../../../../common/utils/Notifications';
import AssessmentTypes from '../../../../../model/AssessmentTypes';
import AvailabilityRuleTypes from '../../../../../model/AvailabilityRuleTypes';
import LearningPlanStatus, {
  OnHold,
  Published,
} from '../../../../../model/LearningPlanStatus';
import {
  isLearningPlanSyntheticParent,
  isLearningSpaceLPSyntheticParent,
  isLearningSpaceLPTask,
  nodeTypes,
} from '../../../../../model/LearningPlanTreeTypes';
import {
  addNodeBuildMeta,
  isCategory,
  isTask,
  resolveNodeId,
  typeNames,
} from '../../../../../model/LearningTaskPlanTreeTypeNames';

import LearningPlanTreeFilters from '../../../common/LearningPlanTreeFilters';
import DueDateRules from '../../../model/DueDateRules';
import TasksTypes from '../../../model/TasksTypes';
import createLearningSpaceLPGql from '../../data/createLearningSpaceLP.graphql';
import createLearningSpaceLPTaskGql from '../../data/createLearningSpaceLPTask.graphql';
import deleteLearningSpaceLPTaskGql from '../../data/deleteLearningSpaceLPTask.graphql';
import dumbMutation from '../../data/dumbMutation.graphql';
import learningSpaceLPGql from '../../data/learningSpaceLP.graphql';
import learningSpaceLPTaskGql from '../../data/learningSpaceLPTask.graphql';
import staffLearningSpaceLearningPlansListGql from '../../data/staffLearningSpaceLearningPlansList.graphql';
import staffLearningSpaceLearningPlanTreeGql from '../../data/staffLearningSpaceLearningPlanTree.graphql';
import updateLearningSpaceLPGql from '../../data/updateLearningSpaceLP.graphql';
import updateLearningSpaceLPTaskGql from '../../data/updateLearningSpaceLPTask.graphql';
import updateSelectedPlansGql from '../../data/updateSelectedPlans.graphql';
import LearningPlanTreeRightActionsButtons from './LearningPlanTreeRightActionsButtons';
import LearningSpaceLPEditForm from './LearningSpaceLPEditForm';
import SelectLearningPlansModal from './SelectLearningPlansModal';
import { IProgramSubjectTaskTypes } from '../../MyLearningSpaceStaffClass';
import { IActionsAddonAction } from '../../../../../common/components/dataViews/NextTree/addons/ActionsAddon/ActionsAddon';
import HintAddon from '../../../../../common/components/dataViews/NextTree/addons/HintAddon';
import { OneDecimal } from '../../../../../model/GradeScaleRoundingStatus';
import useProgramGroupTaskTypes from '../../../../../common/data/hooks/useProgramGroupTaskTypes';
import { TIconType } from '../../../../../common/components/utils/Icon';
import { handleDummyAction } from '../../../../../common/components/utils/Dummy';
import { Active, Deleted } from '../../../../../model/StatusWithDraft';
import StatusAddon from '../../../../../common/components/dataViews/NextTree/addons/StatusAddon';

const MAX_NESTED_CATEGORIES = 2;

const TREE_NAME = 'learningSpaceLearningPlan';

export interface ILearningPlanTree {
  classId: number;
  intakeProgramSubjectId?: number;
  subjectName?: string;
  programGroupId?: number;
  programGroupTypeId?: number;
  programSubjectTaskTypes?: IProgramSubjectTaskTypes[];
  code?: string;
  programName?: string;
  isSubmissionEnabled?: boolean;
  activityName?: string;
  participant?: string;
  intakeId: number;
  ecnSubjectId: number;
}

const LearningPlanTree: React.FC<Readonly<ILearningPlanTree>> = ({
  subjectName,
  code,
  programName,
  classId,
  programGroupId,
  programGroupTypeId,
  programSubjectTaskTypes = [],
  intakeProgramSubjectId,
  isSubmissionEnabled = true,
  activityName = '',
  participant,
  intakeId,
  ecnSubjectId,
}) => {
  const { url, path } = useRouteMatch();
  const history = useHistory();

  const { programGroupTaskTypesById } = useProgramGroupTaskTypes(
    Number(programGroupTypeId),
  );

  const [filters, setFilters] = useState({
    learningStatus: LearningPlanStatus.BasicDefault,
    status: LearningPlanStatus.BasicDefault,
    tasksType: TasksTypes.BasicDefault,
    searchQuery: '',
  });

  const [progGrpTaskTypeId, setProgGrpTaskTypeId] = useState(null);

  const queryVariables = useMemo(() => {
    const { tasksType, status, learningStatus } = filters;
    return {
      learningStatus,
      status,
      tasksType,
      classId,
      intakeProgramSubjectId,
    };
  }, [intakeProgramSubjectId, classId, filters]);

  const gqlOptions = {
    refetchQueries: [
      {
        query: staffLearningSpaceLearningPlanTreeGql,
        variables: queryVariables,
      },
    ],
    awaitRefetchQueries: true,
  };

  const [updateSelectedPlans, { loading: uspLoading }] = useMutation(
    updateSelectedPlansGql,
    gqlOptions,
  );

  const [createLearningSpaceLP, { loading: clpLoading }] = useMutation(
    createLearningSpaceLPGql,
  );

  const [updateLearningSpaceLP, { loading: ulpLoading }] = useMutation(
    updateLearningSpaceLPGql,
    gqlOptions,
  );

  const [deleteLearningSpaceLPTask, { loading: dlspLoading }] = useMutation(
    deleteLearningSpaceLPTaskGql,
    gqlOptions,
  );

  const [deleteCategory, { loading: dcLoading }] = useMutation(
    deleteCategoryGql,
    gqlOptions,
  );

  const { loading: lpLoading, data } = useQuery(
    staffLearningSpaceLearningPlansListGql,
    {
      variables: {
        programGroupId,
        classId,
        intakeProgramSubjectId,
      },
      skip: !programGroupId,
      fetchPolicy: 'no-cache',
    },
  );

  const plansList = useMemo(
    () => get(data, 'staffLearningSpaceLearningPlansList', []),
    [data],
  );

  const [selectedPlans, setSelectedPlans] = useState(plansList);

  useEffect(() => {
    if (plansList !== selectedPlans) {
      setSelectedPlans(plansList);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plansList]);

  const [
    isSelectLearningPlanModalOpen,
    setSelectLearningPlanModalState,
  ] = useState(false);

  const t = useT();

  const makeTasksTypeMatcher = useCallback(
    () =>
      filters.tasksType
        ? ({ enrolmentStatus }) =>
            enrolmentStatus === undefined ||
            includes(filters.tasksType, enrolmentStatus)
        : null,
    [filters.tasksType],
  );

  const makeStatusMatcher = useCallback(
    () =>
      filters.status
        ? ({ status }) =>
            status === undefined || includes(filters.status, status)
        : null,
    [filters.status],
  );

  const makeNameQueryMatcher = useCallback(
    () =>
      filters.searchQuery
        ? node => catsearch(node.name, filters.searchQuery)
        : null,
    [filters.searchQuery],
  );

  const predicatesMeta = useMemo(
    () => [
      {
        valuePropName: 'tasksType',
        predicate: makeTasksTypeMatcher,
        includeChildren: false,
        collectVisibleIds: !!filters.tasksType?.length && !filters.tasksType,
      },
      {
        valuePropName: 'status',
        predicate: makeStatusMatcher,
        includeChildren: false,
        collectVisibleIds: !!filters.status?.length && !filters.searchQuery,
      },
      {
        valuePropName: 'searchQuery',
        predicate: makeNameQueryMatcher,
        includeChildren: true,
        collectVisibleIds: !!filters.searchQuery,
      },
    ],
    [
      filters.status,
      filters.searchQuery,
      filters.tasksType,
      makeNameQueryMatcher,
      makeStatusMatcher,
      makeTasksTypeMatcher,
    ],
  );

  const handleAddLearningSpaceLP = useCallback(() => {
    history.push(`${url}/add`);
  }, [history, url]);

  const handleOpenSelectLearningPlanModal = useCallback(
    () => setSelectLearningPlanModalState(true),
    [setSelectLearningPlanModalState],
  );
  const rightActionButtons = useMemo(
    () => (
      <LearningPlanTreeRightActionsButtons
        isDropdown={!!plansList.length}
        loading={lpLoading || uspLoading}
        onAddPlan={handleAddLearningSpaceLP}
        onOpen={handleOpenSelectLearningPlanModal}
      />
    ),
    [
      handleAddLearningSpaceLP,
      handleOpenSelectLearningPlanModal,
      plansList,
      lpLoading,
      uspLoading,
    ],
  );

  const handleCloseModal = useCallback(
    () => setSelectLearningPlanModalState(false),
    [setSelectLearningPlanModalState],
  );

  const handleSelectedLearningPlansChanged = useCallback(
    async plans => {
      setSelectLearningPlanModalState(false);
      try {
        await updateSelectedPlans({
          variables: {
            classId,
            intakeProgramSubjectId,
            params: plans,
          },
        });
        setSelectedPlans(plans);
      } catch (e) {
        Notifications.error(t('Error'), extractErrorMessage(e, t), t);
      }
    },
    [
      t,
      updateSelectedPlans,
      classId,
      setSelectedPlans,
      setSelectLearningPlanModalState,
      intakeProgramSubjectId,
    ],
  );

  const goToLearningPlanTree = useCallback(() => {
    history.push(url);
  }, [history, url]);

  const handleCreateUpdateLearningPlan = useCallback(
    async entity => {
      const { id, name, status } = entity;
      let action;
      let options;

      if (id) {
        action = updateLearningSpaceLP;
        options = {
          variables: {
            id,
            params: {
              name,
              status,
            },
          },
        };
      } else {
        action = createLearningSpaceLP;
        options = {
          variables: {
            params: {
              name,
              status,
              classId,
              programGroupId,
            },
          },
        };
      }
      await handleFormErrors(action(options), t);
      history.push(url);
    },
    [
      history,
      t,
      createLearningSpaceLP,
      updateLearningSpaceLP,
      classId,
      programGroupId,
      url,
    ],
  );

  const handleAddTask = useCallback(
    (typeId, setSelectedNode) => node => {
      setProgGrpTaskTypeId(typeId);
      if (isTask(node) || isLearningSpaceLPTask(node) || isCategory(node)) {
        setSelectedNode(node);
      } else {
        setSelectedNode(null);
      }
      history.push(`${url}/${node.id}/add/learningSpaceLPTask`);
    },
    [history, setProgGrpTaskTypeId, url],
  );

  const handleAddCategory = useCallback(
    setSelectedNode => node => {
      if (isCategory(node)) {
        history.push(`${url}/${node.id}/add/category`);
        setSelectedNode(node);
      } else {
        setSelectedNode(null);
        history.push(`${url}/add/category`);
      }
    },
    [history, url],
  );

  const createActions = useCallback(
    setSelectedNode =>
      map(programSubjectTaskTypes, ({ id, task, icon }) => ({
        id,
        icon: { name: icon, type: 'pgt' as TIconType },
        title: t(`Add #{task}`, {
          task,
        }),
        action: handleAddTask(id, setSelectedNode),
        className: '',
      })),
    [handleAddTask, t, programSubjectTaskTypes],
  );

  const handleDelete = useCallback(
    async entity => {
      let action;

      if (isLearningSpaceLPTask(entity)) {
        action = deleteLearningSpaceLPTask;
      }

      if (isCategory(entity)) {
        action = deleteCategory;
      }

      if (action) {
        const response = await handleFormErrors(
          action({ variables: { id: entity.id } }),
        );
        Notifications.success(t('Updated Successfully'), '', t);
        return response;
      }
    },
    [deleteLearningSpaceLPTask, deleteCategory, t],
  );

  const renderNodeAddons = useCallback(
    (node, meta, { setSelectedNode }) => {
      const { isEditSessionActive, nodesMeta } = meta;

      const { hasNestedCategory, hasChild } = nodesMeta;

      const addons: JSX.Element[] = [];
      const actions: IActionsAddonAction[] = [];

      const _actions = createActions(setSelectedNode);

      if (
        !isEditSessionActive ||
        node.status === LearningPlanStatus.Deleted.value
      ) {
        return addons;
      }

      if (
        isCategory(node) &&
        !node.learningPlanId &&
        nodesMeta.nestedCategories <= MAX_NESTED_CATEGORIES &&
        !nodesMeta.hasChildTask
      ) {
        actions.push({
          id: `add_category_${node.id}`,
          title: t('Add Category'),
          action: handleAddCategory(setSelectedNode),
        });
      }

      if (isTask(node) || isLearningSpaceLPTask(node)) {
        actions.push({
          id: `copy_task_${node.id}`,
          title: t('Copy'),
          action: handleAddTask(node.progGrpTaskTypeId, setSelectedNode),
        });
      }

      if (
        !isRootNode(node) &&
        !hasChild &&
        (isLearningSpaceLPTask(node) ||
          (isCategory(node) && !node?.learningPlanId))
      ) {
        actions.push({
          id: `delete_${node.id}`,
          title: t('Delete'),
          action: handleDelete,
          className: 'text-danger',
        });
      }

      if (isCategory(node) && !hasNestedCategory) {
        if (!isEmpty(_actions)) {
          !isEmpty(actions) &&
            actions.push({
              id: `separator`,
              title: t('Add task'),
              disabled: true,
              className: styles.menuDivider,
            });
          actions.push(..._actions);
        } else {
          addons.push(
            <HintAddon
              iconHoverTitle={t(
                'To add tasks, you should select at least one task type on LMS tab of #{activityName}',
                { activityName: `[${t(activityName)}]` },
              )}
              node={node}
            />,
          );
        }
      }

      if (!isEmpty(actions)) {
        addons.push(
          <ActionsAddon
            key={`actions_${node.id}`}
            actions={actions}
            node={node}
          />,
        );
      }

      return addons;
    },
    [
      handleAddCategory,
      activityName,
      handleDelete,
      handleAddTask,
      t,
      createActions,
    ],
  );

  const {
    programGroupTaskTypes,
    loading: loadingProgramGroupTaskTypes,
  } = useProgramGroupTaskTypes(Number(programGroupTypeId));

  const isLoading = some([
    uspLoading,
    clpLoading,
    ulpLoading,
    dlspLoading,
    dcLoading,
    loadingProgramGroupTaskTypes,
  ]);

  const resolveNodeIconName = useCallback(
    node => {
      if (isLearningPlanSyntheticParent(node)) {
        return 'stack-text';
      }

      if (isLearningSpaceLPSyntheticParent(node)) {
        return 'stack-user';
      }

      if (isTask(node) || isLearningSpaceLPTask(node)) {
        return {
          type: 'pgt',
          className: programGroupTaskTypesById[node?.progGrpTaskTypeId]?.icon,
        };
      }

      return '';
    },
    [programGroupTaskTypesById],
  );

  const deleteLearningPlan = useCallback(
    async ({ id }) => {
      const modified = selectedPlans.map(item => {
        const updatedItem = { ...item };
        if (item.learningPlanId === id) {
          updatedItem.status =
            item.status === Published.value ? OnHold.value : Published.value;
        }

        return updatedItem;
      });

      await handleSelectedLearningPlansChanged(modified);
    },

    [handleSelectedLearningPlansChanged, selectedPlans],
  );

  const renderLearningPlanAddons = useCallback(
    (node, meta) => {
      const { isEditSessionActive } = meta;

      const addons: JSX.Element[] = [];

      if (isEditSessionActive && isLearningPlanSyntheticParent(node)) {
        addons.push(
          <DeleteAddon
            key={`actions_${node.id}`}
            node={node}
            onDelete={deleteLearningPlan}
          />,
        );
      }

      addons.push(
        <StatusAddon
          key={`status_${node.id}`}
          customActiveStatus={Published}
          customDeletedStatus={LearningPlanStatus.Deleted}
          customStatuses={LearningPlanStatus.BasicByValue}
          status={node.status}
        />,
      );

      return addons;
    },
    [deleteLearningPlan],
  );

  const renderLearningSpaceLP = useCallback(node => {
    if (isLearningSpaceLPSyntheticParent(node)) {
      const { id, status } = node;
      return [
        <StatusAddon
          key={`status_${id}`}
          customActiveStatus={Published}
          customDeletedStatus={LearningPlanStatus.Deleted}
          customStatuses={LearningPlanStatus.BasicByValue}
          status={status}
        />,
      ];
    }
    return [];
  }, []);

  return (
    <Switch>
      <Route exact path={`${path}/add`}>
        <Breadcrumbs.Anchor route={`#{url}/add`} title={t('Add Learning Plan')}>
          <ContentPanel title={t('Add Learning Plan')}>
            <LearningSpaceLPEditForm
              hasTitle={false}
              node={{ status: LearningPlanStatus.Published.value }}
              onCancel={goToLearningPlanTree}
              onSubmit={handleCreateUpdateLearningPlan}
            />
          </ContentPanel>
        </Breadcrumbs.Anchor>
      </Route>
      <Route key={path} path={path}>
        <ContentPanel
          rightActionButton={rightActionButtons}
          title={t('Learning Plan')}
        >
          <LoadingMask active={isLoading}>
            <GqlFullCrudTree
              nodes={{
                task: {
                  isSubmissionEnabled,
                  programGroupId,
                  programGroupTypeId,
                  isThisNodeType: isTask,
                  component: TaskEditForm,
                  hasWrapper: false,
                  hasTitle: false,
                  isReadOnly: true,
                  gql: {
                    single: learningPlanTaskGql,
                    create: dumbMutation,
                    update: dumbMutation,
                    getParentGql: () => learningPlanCategoryGql,
                  },
                  renderNodeAddons,
                  ecnSubjectId,
                  treeTitle: t(
                    `#{programName} #{subjectName} #{code} learning plan tree`,
                    {
                      programName,
                      subjectName,
                      code,
                    },
                  ),
                  classId,
                  intakeProgramSubjectId,
                  isLearningSpace: true,
                },
                learningSpaceLPTask: {
                  isSubmissionEnabled,
                  programGroupId,
                  programGroupTaskTypes,
                  programGroupTypeId,
                  title: t('Task'),
                  isThisNodeType: isLearningSpaceLPTask,
                  component: TaskEditForm,
                  ecnSubjectId,
                  hasWrapper: false,
                  hasTitle: false,
                  intakeId,
                  treeTitle: t(
                    `#{programName} #{subjectName} #{code} learning plan tree`,
                    {
                      programName,
                      subjectName,
                      code,
                    },
                  ),
                  classId,
                  intakeProgramSubjectId,
                  isLearningSpace: true,
                  additionalNodeFields: {
                    __typename: 'LearningSpaceLPTask',
                    progGrpTaskTypeId,
                    classId,
                    submission: true,
                    responseType: 'SIMPLE_TEXT',
                    attachments: true,
                    attachmentsFrom: 1,
                    attachmentsTo: 1,
                    audioSubmissions: false,
                    audioSubmissionsFrom: 1,
                    audioSubmissionsTo: 1,
                    videoSubmissions: false,
                    videoSubmissionsFrom: 1,
                    videoSubmissionsTo: 1,
                    multipleSubmissions: true,
                    assessed: true,
                    rounding: OneDecimal.value,
                    assessmentType: AssessmentTypes.Mark.value,
                    assessmentOptions: [],
                    lsLpTaskAssignedStudents: {
                      allStudents: 1,
                      availabilityRule: AvailabilityRuleTypes.AnyTime.value,
                      dueDateRule: DueDateRules.ANY_TIME.value,
                      groupSubmission: false,
                    },
                    taskCalculate: [],
                  },
                  gql: {
                    single: learningSpaceLPTaskGql,
                    create: createLearningSpaceLPTaskGql,
                    update: updateLearningSpaceLPTaskGql,
                    getParentGql: node => {
                      if (isTask(node)) {
                        return learningPlanTaskGql;
                      }
                      if (isLearningSpaceLPTask(node)) {
                        return learningSpaceLPTaskGql;
                      }
                      return learningPlanCategoryGql;
                    },
                  },
                  renderNodeAddons,
                  participant,
                },
                category: {
                  title: 'Category',
                  isThisNodeType: isCategory,
                  component: CategoryEditForm,
                  treeTitle: t(
                    `#{programName} #{subjectName} #{code} learning plan tree`,
                    {
                      programName,
                      subjectName,
                      code,
                    },
                  ),
                  classId,
                  intakeProgramSubjectId,
                  isLearningSpace: true,
                  hasTitle: false,
                  hasWrapper: false,
                  canCreateUnder: node =>
                    isLearningSpaceLPSyntheticParent(node),
                  gql: {
                    single: learningPlanCategoryGql,
                    singleVariables: { includeMySpace: true },
                    getParentGql: node =>
                      node && isCategory(node)
                        ? learningPlanCategoryGql
                        : learningSpaceLPGql,
                    create: createCategoryGql,
                    update: updateCategoryGql,
                  },
                  renderNodeAddons,
                  programGroupId,
                  programGroupTypeId,
                  treeName: TREE_NAME,
                },
                learningSpaceLP: {
                  __typename: 'LearningSpaceTask',
                  isThisNodeType: isLearningSpaceLPSyntheticParent,
                  component: LearningSpaceLPEditForm,
                  hasTitle: false,
                  gql: {
                    single: learningSpaceLPGql,
                    update: updateLearningSpaceLPGql,
                  },
                },
                learningPlan: {
                  __typename: 'LearningSpaceTask',
                  isThisNodeType: isLearningPlanSyntheticParent,
                  hasTitle: false,
                  renderNodeAddons: renderLearningPlanAddons,
                },
              }}
              tree={{
                hasTitle: false,
                rootName: t(
                  `#{programName} #{subjectName} #{code} learning plan tree`,
                  {
                    programName,
                    subjectName,
                    code,
                  },
                ),
                customStatuses: LearningPlanStatus.TreeStatusesByValue,
                customDeletedStatus: LearningPlanStatus.Deleted,
                key: TREE_NAME,
                gql: {
                  query: staffLearningSpaceLearningPlanTreeGql,
                  variables: queryVariables,
                  skip: !intakeProgramSubjectId,
                },
                adapter: {
                  resolveNodeId,
                  resolveNodeParentId,
                  nodeIsLeaf,
                  resolveNodeClassName,
                },
                plugins: {
                  nodeIsSelectable,
                  addNodeBuildMeta,
                  resolveNodeIconName,
                },
                filter: {
                  component: LearningPlanTreeFilters,
                  value: filters,
                  predicatesMeta,
                  onChange: setFilters,
                },
                treeLayoutType: TreeLayoutTypeEnum.TREE_PAGE,
              }}
            />
          </LoadingMask>
          {isSelectLearningPlanModalOpen && (
            <SelectLearningPlansModal
              isSelectLearningPlanModalOpen={isSelectLearningPlanModalOpen}
              plansList={selectedPlans}
              title={t('Select Learning Plan')}
              onCancel={handleCloseModal}
              onChanged={handleSelectedLearningPlansChanged}
            />
          )}
        </ContentPanel>
      </Route>
    </Switch>
  );
};

export default LearningPlanTree;

const nodeIsSelectable = node =>
  !isRootNode(node) && !isLearningPlanSyntheticParent(node);

const resolveNodeParentId = node => {
  const {
    parentId,
    learningPlanCategoryId,
    learningPlanId,
    learningSpaceLpId,
  } = node;

  if (isCategory(node) && parentId) {
    return `${typeNames.category}${parentId}`;
  }

  if (isCategory(node) && learningPlanId) {
    return `${nodeTypes.learningPlanSyntheticParent}${learningPlanId}`;
  }

  if (isCategory(node) && learningSpaceLpId) {
    return `${nodeTypes.learningSpaceLPSyntheticParent}${learningSpaceLpId}`;
  }

  if (isTask(node) && parentId) {
    return `${typeNames.task}${parentId}`;
  }

  if (isTask(node) && learningPlanCategoryId) {
    return `${typeNames.category}${learningPlanCategoryId}`;
  }

  if (isLearningSpaceLPTask(node)) {
    return `${typeNames.category}${learningPlanCategoryId}`;
  }

  return null;
};

const resolveNodeClassName = node => {
  if (isLearningPlanSyntheticParent(node)) {
    return [styles.lpTask, 'jstree-node-bold'];
  }

  if (isLearningSpaceLPSyntheticParent(node)) {
    return [styles.lsLpTask, 'jstree-node-bold'];
  }

  if (isRootNode(node)) {
    return 'jstree-node-bold';
  }

  if (isLearningSpaceLPTask(node)) {
    return [styles.lsLpTask, 'jstree-node-italic'];
  }

  if (isTask(node)) {
    return styles.lpTask;
  }

  return '';
};

const nodeIsLeaf = node => isTask(node) || isLearningSpaceLPTask(node);
